<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Animal Record</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <span class="title">Animal Record</span>
      <a href="index.html" class="back-link">←</a>
    </header>

    <main class="animal-detail">
    <div class="animal-header">
      <h1 id="animal-name">Loading...</h1>
      <img id="animal-photo" src="assets/images/placeholder.jpg" alt="Animal Photo" />
      <div class="animal-info">
        <div class="info-row">
          <strong>Species:</strong>
          <span id="animal-species"></span>
        </div>
        <div class="info-row">
          <strong>Group:</strong>
          <span id="animal-group"></span>
        </div>
        <div class="info-row">
          <strong>Status:</strong>
          <span id="animal-status"></span>
        </div>
        <div class="info-row note-row">
          <strong>Notes:</strong>
          <span id="animal-notes"></span>
        </div>
      </div>
    </div>

    <div class="qr-container">
      <canvas id="qrcode"></canvas>
    </div>

    <hr>

    <section>
      <h3>Today's Logs</h3>
      <div id="today-logs"></div>
    </section>

    <section>
      <button id="toggle-logs">Show Recent Logs</button>
      <div id="recent-logs-section" style="display:none;">
        <h3>Recent Logs</h3>
        <div id="recent_logs"></div>
      </div>
    </section>
  </main>
  </div>

 <!-- Floating Action Button and Actions -->
<div class="fab-container">
  <div id="fab-actions" class="fab-actions">
    <button class="fab-action" onclick="document.getElementById('log-modal').style.display='flex'">
      <span class="material-icons">note_add</span>
      <span class="label">Add Log</span>
    </button>
    <button class="fab-action" onclick="openEditModal()">
      <span class="material-icons">edit</span>
      <span class="label">Edit Animal</span>
    </button>
    <button class="fab-action" onclick="window.print()">
      <span class="material-icons">print</span>
      <span class="label">Print Sheet</span>
    </button>
    <button class="fab-action" onclick="openMedicalTracker()">
      <span class="material-icons">medical_services</span>
      <span class="label">Medical Tracker</span>
    </button>
    <button class="fab-action" onclick="openReportModal()">
      <span class="material-icons">description</span>
      <span class="label">Export Animal Report</span>
    </button>
  </div>
  <button class="fab-main" onclick="toggleFAB()">
    <span class="material-icons">add</span>
    <span class="label">Actions</span>
  </button>
</div>

  <!-- Log Modal -->
  <div id="log-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="document.getElementById('log-modal').style.display='none'">✖</span>
      <h2>Add Daily Log</h2>
      <form id="log-form">
        <label class="custom-checkbox">
          <input type="checkbox" name="fed" />
          <span class="checkmark"></span> Fed
        </label>
        <label class="custom-checkbox">
          <input type="checkbox" name="cleaned" />
          <span class="checkmark"></span> Cleaned
        </label><br>
        <label>Medication Given:<br>
          <input type="text" name="medication" placeholder="e.g. Wormer, Painkiller" />
        </label><br>
        <label>Notes:<br>
          <textarea name="notes" rows="3" placeholder="e.g. Slight limp, feathers checked"></textarea>
        </label>
        <button type="submit">Add Log</button>
      </form>
      <div id="log-feedback" style="margin-top:1rem;"></div>
    </div>
  </div>

  <!-- Edit Modal -->
  <div id="edit-animal-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeEditModal()">✖</span>
      <h2>Edit Animal</h2>
      <form id="edit-animal-form">
        <label>Name:<br>
          <input type="text" name="name" required />
        </label><br>
        <label>Species:<br>
          <input type="text" name="species" required />
        </label><br>
        <label>Group:<br>
          <select name="Group" required>
            <option value="">--Select Group--</option>
            <option value="Livestock">Livestock</option>
            <option value="Other (Miscellaneous)">Other (Miscellaneous)</option>
            <option value="Owls">Owls</option>
            <option value="Parrots">Parrots</option>
            <option value="Raptor">Raptor</option>
            <option value="Waterfoul">Waterfoul</option>
          </select>
        </label><br>
        <label>Status:<br>
          <select name="status" required>
            <option value="All Clear">All Clear</option>
            <option value="Under Observation">Under Observation</option>
            <option value="Unwell">Unwell</option>
            <option value="Vet Booked">Vet Booked</option>
            <option value="In Treatment">In Treatment</option>
            <option value="Recovery">Recovery</option>
            <option value="Ongoing Condition">Ongoing Condition</option>
            <option value="Palliative">Palliative</option>
            <option value="Quarantined">Quarantined</option>
            <option value="Transferred">Transferred</option>
            <option value="Deceased">Deceased</option>
          </select>
        </label><br>
        <label>Photo:<br>
          <div style="margin-bottom: 0.5rem;">
            <button type="button" id="edit-upload-tab" onclick="switchEditPhotoMethod('upload')" style="background: #446c35; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px 0 0 4px; cursor: pointer;">Upload File</button>
            <button type="button" id="edit-url-tab" onclick="switchEditPhotoMethod('url')" style="background: #ccc; color: #666; border: none; padding: 0.5rem 1rem; border-radius: 0 4px 4px 0; cursor: pointer;">Enter URL</button>
          </div>

          <div id="edit-upload-method" style="display: block;">
            <input type="file" name="photo" accept="image/*" id="edit-photo-upload" />
            <small style="color: #666; font-size: 0.9rem;">Choose a new image file (JPG, PNG, etc.)</small>
          </div>

          <div id="edit-url-method" style="display: none;">
            <input type="url" name="photo_url" id="edit-photo-url" placeholder="https://example.com/image.jpg" />
            <small style="color: #666; font-size: 0.9rem;">Enter a direct link to an image</small>
          </div>
        </label><br>

        <div id="edit-photo-preview" style="display: none; margin-bottom: 1rem;">
          <img id="edit-preview-image" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 2px solid #ddd;" />
          <button type="button" onclick="removeEditPhoto()" style="display: block; margin-top: 0.5rem; background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Remove New Photo</button>
        </div>

        <div id="current-photo-display" style="margin-bottom: 1rem;">
          <label style="font-weight: bold;">Current Photo:</label><br>
          <img id="current-photo-image" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 2px solid #ddd; margin-top: 0.5rem;" />
        </div>
        <label>Notes:<br>
          <textarea name="notes" rows="4"></textarea>
        </label><br>
        <button type="submit">Save Changes</button>
      </form>
    </div>
  </div>

  <!-- Report Generation Modal -->
  <div id="report-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeReportModal()">✖</span>
      <h2>Generate Animal Report</h2>
      <p>Select the sections to include in your report:</p>

      <div class="report-sections">
        <div class="section-toggle">
          <button type="button" class="btn-toggle active" data-section="profile">
            <span class="material-icons">pets</span>
            Animal Profile
          </button>
          <small>Basic information and photo (always included)</small>
        </div>

        <div class="section-toggle">
          <button type="button" class="btn-toggle" data-section="logs">
            <span class="material-icons">event_note</span>
            Daily Logs
          </button>
          <small>Recent daily care logs and observations</small>
        </div>

        <div class="section-toggle">
          <button type="button" class="btn-toggle" data-section="medical">
            <span class="material-icons">medical_services</span>
            Medical Episodes & Interventions
          </button>
          <small>Medical history and treatment records</small>
        </div>

        <div class="section-toggle">
          <button type="button" class="btn-toggle" data-section="adoption">
            <span class="material-icons">favorite</span>
            Adoption History
          </button>
          <small>Adoption applications and status</small>
        </div>

        <div class="section-toggle">
          <button type="button" class="btn-toggle" data-section="donation">
            <span class="material-icons">volunteer_activism</span>
            Donation History
          </button>
          <small>Related donations and sponsorships</small>
        </div>
      </div>

      <div class="report-actions">
        <button type="button" class="btn-secondary" onclick="closeReportModal()">Cancel</button>
        <button type="button" class="btn-primary" onclick="generateReport()">
          <span class="material-icons">download</span>
          Generate Report
        </button>
      </div>
    </div>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="index.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Document Management</a></li>
      <li><a href="#" onclick="openMedicalTracker()"><span class="material-icons">medical_services</span> Medical Tracker</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff/Volunteer Module</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Donations and Adoptions Module</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts Module</a></li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>

  <!-- FAB Toggle Function - Global scope -->
  <script>
    let fabOpen = false;

    function toggleFAB() {
      console.log('toggleFAB called, current state:', fabOpen);
      const actions = document.getElementById('fab-actions');
      const fabMain = document.querySelector('.fab-main');

      if (!actions) {
        console.error('fab-actions element not found');
        return;
      }

      fabOpen = !fabOpen;
      actions.style.display = fabOpen ? 'flex' : 'none';
      fabMain.classList.toggle('active', fabOpen);

      console.log('New state:', fabOpen, 'Display:', actions.style.display);
    }

    function toggleMenu() {
      console.log('toggleMenu called');
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (!sideMenu || !overlay) {
        console.error('Menu elements not found:', { sideMenu, overlay });
        return;
      }

      const isOpen = sideMenu.classList.contains('open');
      console.log('Menu is currently:', isOpen ? 'open' : 'closed');

      if (isOpen) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
        console.log('Menu closed');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
        console.log('Menu opened');
      }
    }

    function openEditModal() {
      // This will be populated once currentAnimal is loaded
      if (!window.currentAnimal) {
        alert('Animal data not loaded yet. Please wait a moment and try again.');
        return;
      }
      const form = document.getElementById('edit-animal-form');
      form.name.value = window.currentAnimal.name;
      form.species.value = window.currentAnimal.species;
      form.Group.value = window.currentAnimal.Group || window.currentAnimal.group || '';
      form.status.value = window.currentAnimal.status;
      form.notes.value = window.currentAnimal.notes || '';

      // Show current photo
      const currentPhotoImg = document.getElementById('current-photo-image');
      const currentPhotoDisplay = document.getElementById('current-photo-display');
      if (window.currentAnimal.photo_url) {
        currentPhotoImg.src = window.currentAnimal.photo_url;
        currentPhotoDisplay.style.display = 'block';
      } else {
        currentPhotoDisplay.style.display = 'none';
      }

      // Reset photo inputs and switch to upload method by default
      document.getElementById('edit-photo-upload').value = '';
      document.getElementById('edit-photo-url').value = '';
      document.getElementById('edit-photo-preview').style.display = 'none';
      switchEditPhotoMethod('upload');

      document.getElementById('edit-animal-modal').style.display = 'flex';
    }

    function openMedicalTracker() {
      const urlParams = new URLSearchParams(window.location.search);
      const animalId = urlParams.get('id');
      if (animalId) {
        window.location.href = `medical-tracker.html?id=${animalId}`;
      } else {
        alert('Animal ID not found. Please try again.');
      }
    }

    // Switch between upload and URL methods for edit modal
    function switchEditPhotoMethod(method) {
      const uploadTab = document.getElementById('edit-upload-tab');
      const urlTab = document.getElementById('edit-url-tab');
      const uploadMethod = document.getElementById('edit-upload-method');
      const urlMethod = document.getElementById('edit-url-method');

      if (method === 'upload') {
        uploadTab.style.background = '#446c35';
        uploadTab.style.color = 'white';
        urlTab.style.background = '#ccc';
        urlTab.style.color = '#666';
        uploadMethod.style.display = 'block';
        urlMethod.style.display = 'none';
        // Clear URL input
        document.getElementById('edit-photo-url').value = '';
      } else {
        urlTab.style.background = '#446c35';
        urlTab.style.color = 'white';
        uploadTab.style.background = '#ccc';
        uploadTab.style.color = '#666';
        urlMethod.style.display = 'block';
        uploadMethod.style.display = 'none';
        // Clear file input
        document.getElementById('edit-photo-upload').value = '';
      }
      // Clear preview
      document.getElementById('edit-photo-preview').style.display = 'none';
    }

    // Photo handling functions for edit modal
    document.addEventListener('DOMContentLoaded', function() {
      const editPhotoUpload = document.getElementById('edit-photo-upload');
      const editPhotoUrl = document.getElementById('edit-photo-url');

      if (editPhotoUpload) {
        editPhotoUpload.addEventListener('change', function(e) {
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
              document.getElementById('edit-preview-image').src = e.target.result;
              document.getElementById('edit-photo-preview').style.display = 'block';
            };
            reader.readAsDataURL(file);
          }
        });
      }

      if (editPhotoUrl) {
        editPhotoUrl.addEventListener('input', function(e) {
          const url = e.target.value;
          if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
            document.getElementById('edit-preview-image').src = url;
            document.getElementById('edit-photo-preview').style.display = 'block';
          } else {
            document.getElementById('edit-photo-preview').style.display = 'none';
          }
        });
      }
    });

    function removeEditPhoto() {
      document.getElementById('edit-photo-upload').value = '';
      document.getElementById('edit-photo-url').value = '';
      document.getElementById('edit-photo-preview').style.display = 'none';
    }

    // Convert file to base64
    function fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
      });
    }
  </script>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    const urlParams = new URLSearchParams(window.location.search);
    const animalId = urlParams.get('id');
    let currentAnimal = null;

    // Make currentAnimal available globally
    window.currentAnimal = null;

    async function initializeApp() {
      console.log('Starting animal page initialization...');

      // Check authentication
      console.log('Checking authentication...');
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        console.log('User not authenticated, redirecting to login');
        window.location.href = 'login.html';
        return;
      }

      console.log('✅ User authenticated:', user.email);

      if (!animalId) {
        document.body.innerHTML = '<h2>No animal ID provided.</h2>';
        return;
      }

      await loadAnimal();
      await loadLogs();
    }

    async function loadAnimal() {
      console.log('Fetching animal with ID:', animalId);

      const { data: animals, error } = await supabase
        .from('animals')
        .select('*')
        .eq('id', animalId);

      if (error) {
        console.error('Error fetching animal:', error);
        document.body.innerHTML = '<h2>Error loading animal.</h2>';
        return;
      }

      if (!animals || animals.length === 0) {
        console.log('Animal not found');
        document.body.innerHTML = '<h2>Animal not found.</h2>';
        return;
      }

      const animal = animals[0];
      console.log('Animal loaded:', animal.name);

      currentAnimal = animal;
      window.currentAnimal = animal;

      document.getElementById('animal-name').textContent = animal.name;
      document.getElementById('animal-species').textContent = animal.species;
      document.getElementById('animal-group').textContent = animal.Group || animal.group || 'Unknown';
      document.getElementById('animal-status').textContent = animal.status;
      document.getElementById('animal-notes').textContent = animal.notes || '—';
      document.getElementById('animal-photo').src = animal.photo_url || 'assets/images/placeholder.jpg';

      const qrLink = `${window.location.origin}${window.location.pathname}?id=${animal.id}`;
      QRCode.toCanvas(document.getElementById('qrcode'), qrLink, { width: 100 });
    }

    function formatLog(log) {
      const dateStr = new Date(log.created_on || '').toLocaleDateString('en-UK');
      return `
        <div class="log-entry">
          <table class="log-table">
            <tr><th>Date:</th><td>${dateStr}</td></tr>
            <tr><th>Fed:</th><td>${log.fed ? 'Yes' : 'No'}</td></tr>
            <tr><th>Cleaned:</th><td>${log.cleaned ? 'Yes' : 'No'}</td></tr>
            <tr><th>Medication:</th><td>${log.medication || 'N/A'}</td></tr>
            <tr><th>Notes:</th><td>${log.notes || '—'}</td></tr>
          </table>
        </div>`;
    }

    async function loadLogs() {
      console.log('Fetching logs for animal:', animalId);

      const { data: logs, error } = await supabase
        .from('daily_logs')
        .select('*')
        .eq('animal_id', animalId)
        .order('created_on', { ascending: false });

      if (error) {
        console.error('Error fetching logs:', error);
        document.getElementById('today-logs').innerHTML = '<p>Error loading logs.</p>';
        document.getElementById('recent_logs').innerHTML = '<p>Error loading logs.</p>';
        return;
      }

      console.log('Logs loaded:', logs.length);

      const today = new Date().toISOString().split('T')[0];
      const todayLogs = logs.filter(log => log.created_on?.split('T')[0] === today);
      const pastLogs = logs.filter(log => log.created_on?.split('T')[0] !== today);

      document.getElementById('today-logs').innerHTML = todayLogs.length
        ? todayLogs.map(formatLog).join('')
        : '<p>No logs today yet.</p>';

      document.getElementById('recent_logs').innerHTML = pastLogs.map(formatLog).join('');
    }

    document.getElementById('edit-animal-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      const data = Object.fromEntries(formData.entries());

      // Handle photo - either file upload or URL
      const photoFile = formData.get('photo');
      const photoUrl = formData.get('photo_url');

      if (photoFile && photoFile.size > 0) {
        // File upload method
        try {
          const base64 = await fileToBase64(photoFile);
          data.photo_url = base64;
        } catch (error) {
          console.error('Error processing photo:', error);
          alert('Error processing photo. Please try again.');
          return;
        }
      } else if (photoUrl && photoUrl.trim()) {
        // URL method
        data.photo_url = photoUrl.trim();
      } else {
        // No new photo provided - preserve existing photo URL
        data.photo_url = window.currentAnimal.photo_url;
      }

      // Remove the photo file from data since we're using photo_url
      delete data.photo;

      const { error } = await supabase
        .from('animals')
        .update(data)
        .eq('id', animalId);

      if (error) {
        alert('Update failed');
        console.error('Error updating animal:', error);
      } else {
        closeEditModal();
        location.reload();
      }
    });

    document.getElementById('log-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      const logData = {
        animal_id: animalId,
        fed: formData.has('fed'),
        cleaned: formData.has('cleaned'),
        medication: formData.get('medication'),
        notes: formData.get('notes')
      };

      const { data, error } = await supabase
        .from('daily_logs')
        .insert([logData]);

      if (error) {
        document.getElementById('log-feedback').textContent = '❌ Error saving log.';
        console.error('Error saving log:', error);
      } else {
        document.getElementById('log-feedback').textContent = '✅ Log saved.';
        e.target.reset();
        document.getElementById('log-modal').style.display = 'none';
        loadLogs();
      }
    });

    document.getElementById('toggle-logs').addEventListener('click', () => {
      const section = document.getElementById('recent-logs-section');
      section.style.display = section.style.display === 'none' ? 'block' : 'none';
      document.getElementById('toggle-logs').textContent = section.style.display === 'none' ? 'Show Recent Logs' : 'Hide Recent Logs';
    });

    // Function to open medical tracker for current animal
    function openMedicalTracker() {
      const urlParams = new URLSearchParams(window.location.search);
      const animalId = urlParams.get('id');

      if (animalId) {
        window.location.href = `medical-tracker.html?id=${animalId}`;
      } else {
        alert('No animal selected. Please select an animal from the Dashboard first.');
      }
    }

    function closeEditModal() {
      document.getElementById('edit-animal-modal').style.display = 'none';
    }

    // Report Generation Functions
    function openReportModal() {
      document.getElementById('report-modal').style.display = 'flex';
      setupReportToggles();
    }

    function closeReportModal() {
      document.getElementById('report-modal').style.display = 'none';
    }

    function setupReportToggles() {
      const toggleButtons = document.querySelectorAll('.btn-toggle');
      toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
          // Don't allow deselecting the profile section (always required)
          if (this.dataset.section === 'profile') return;

          this.classList.toggle('active');
        });
      });
    }

    async function generateReport() {
      // Check if animal data is loaded
      if (!window.currentAnimal) {
        alert('Animal data not loaded yet. Please wait a moment and try again.');
        return;
      }

      const selectedSections = getSelectedSections();
      console.log('Generating report with sections:', selectedSections);
      console.log('Current animal:', window.currentAnimal);

      try {
        // Show loading state
        const generateBtn = document.querySelector('.btn-primary');
        const originalText = generateBtn.innerHTML;
        generateBtn.innerHTML = '<span class="material-icons">hourglass_empty</span> Generating...';
        generateBtn.disabled = true;

        // Fetch data for selected sections
        const reportData = await fetchReportData(selectedSections);
        console.log('Report data fetched:', reportData);

        // Generate HTML for the report
        const reportHTML = generateReportHTML(reportData, selectedSections);
        console.log('Report HTML generated, length:', reportHTML.length);

        // Create enhanced preview window with print/PDF buttons
        const previewWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes');

        const enhancedHTML = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Animal Report - ${(window.currentAnimal && window.currentAnimal.name) || 'Unknown'}</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
    .print-controls {
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 1000;
      display: flex;
      gap: 10px;
    }
    .print-btn, .pdf-btn {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .print-btn:hover, .pdf-btn:hover {
      background: #45a049;
    }
    .pdf-btn {
      background: #2196F3;
    }
    .pdf-btn:hover {
      background: #1976D2;
    }
    .report-content {
      margin-top: 60px;
      padding: 20px;
    }
    @media print {
      .print-controls { display: none !important; }
      .report-content { margin-top: 0; }
      body { margin: 0; }
    }
  </style>
</head>
<body>
  <div class="print-controls">
    <button class="print-btn" onclick="window.print()">
      🖨️ Print
    </button>
    <button class="pdf-btn" onclick="saveToPDF()">
      📄 Save as PDF
    </button>
  </div>
  <div class="report-content">
    ${reportHTML}
  </div>
  <script>
    function saveToPDF() {
      const animalName = (window.opener && window.opener.currentAnimal && window.opener.currentAnimal.name) || 'Animal';
      const filename = animalName.replace(/[^a-zA-Z0-9]/g, '_') + '_Report_' + new Date().toISOString().split('T')[0] + '.pdf';
      const element = document.querySelector('.report-content');

      const opt = {
        margin: 0.5,
        filename: filename,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 0.8,
          backgroundColor: '#ffffff'
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'portrait'
        }
      };

      html2pdf().from(element).set(opt).save();
    }
  </script>
</body>
</html>
`;


        previewWindow.document.write(enhancedHTML);
        previewWindow.document.close();

        // Reset button and close modal
        generateBtn.innerHTML = originalText;
        generateBtn.disabled = false;
        closeReportModal();

      } catch (error) {
        console.error('Error generating report:', error);
        alert('Error generating report: ' + error.message);

        // Reset button
        const generateBtn = document.querySelector('.btn-primary');
        generateBtn.innerHTML = '<span class="material-icons">download</span> Generate Report';
        generateBtn.disabled = false;
      }
    }

    function getSelectedSections() {
      const activeButtons = document.querySelectorAll('.btn-toggle.active');
      return Array.from(activeButtons).map(btn => btn.dataset.section);
    }

    async function fetchReportData(sections) {
      const data = { animal: window.currentAnimal };

      for (const section of sections) {
        switch (section) {
          case 'logs':
            data.logs = await fetchDailyLogs();
            break;
          case 'medical':
            data.medical = await fetchMedicalData();
            break;
          case 'adoption':
            data.adoption = await fetchAdoptionData();
            break;
          case 'donation':
            data.donation = await fetchDonationData();
            break;
        }
      }

      return data;
    }

    async function fetchDailyLogs() {
      try {
        const { data, error } = await supabase
          .from('daily_logs')
          .select('*')
          .eq('animal_id', animalId)
          .order('created_on', { ascending: false })
          .limit(20);

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error fetching daily logs:', error);
        return [];
      }
    }

    async function fetchMedicalData() {
      try {
        // Fetch medical episodes with interventions
        const { data: episodes, error: episodesError } = await supabase
          .from('medical_episodes')
          .select(`
            *,
            medical_interventions (*)
          `)
          .eq('animal_id', animalId)
          .order('created_at', { ascending: false });

        if (episodesError) throw episodesError;
        return episodes || [];
      } catch (error) {
        console.error('Error fetching medical data:', error);
        return [];
      }
    }

    async function fetchAdoptionData() {
      try {
        const { data, error } = await supabase
          .from('adoptions_donations')
          .select('*')
          .eq('animal_id', animalId)
          .eq('donation_type', 'adoption')
          .order('created_at', { ascending: false });

        if (error) throw error;
        console.log('Adoption data:', data);
        return data || [];
      } catch (error) {
        console.error('Error fetching adoption data:', error);
        return [];
      }
    }

    async function fetchDonationData() {
      try {
        const { data, error } = await supabase
          .from('adoptions_donations')
          .select('*')
          .eq('animal_id', animalId)
          .eq('donation_type', 'donation')
          .order('created_at', { ascending: false });

        if (error) throw error;
        console.log('Donation data:', data);
        return data || [];
      } catch (error) {
        console.error('Error fetching donation data:', error);
        return [];
      }
    }

    function generateReportHTML(data, sections) {
      console.log('Generating HTML for data:', data);
      console.log('Selected sections:', sections);

      const animal = data.animal;
      if (!animal) {
        console.error('No animal data provided to generateReportHTML');
        return '<div>Error: No animal data available</div>';
      }

      console.log('Animal photo_url:', animal.photo_url);
      console.log('Full animal object:', animal);

      const currentDate = new Date().toLocaleDateString('en-GB');

      let html = '<div style="font-family: Arial, sans-serif; width: 100%; margin: 0; padding: 0; color: #333; background: white;">' +
        '<div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #4CAF50; padding-bottom: 20px;">' +
          '<h1 style="color: #4CAF50; margin: 0; font-size: 24px;">Cornish Birds of Prey Center</h1>' +
          '<h2 style="color: #666; margin: 10px 0; font-size: 18px;">Animal Report</h2>' +
          '<p style="margin: 5px 0; color: #888; font-size: 12px;">Generated on ' + currentDate + '</p>' +
        '</div>';

      // Animal Profile Section (always included)
      html += '<div style="margin-bottom: 30px;">' +
        '<h3 style="color: #4CAF50; border-bottom: 1px solid #ddd; padding-bottom: 10px;">Animal Profile</h3>' +
        '<div style="display: flex; gap: 20px; margin-top: 15px;">' +
          '<div style="flex: 1;">' +
            '<table style="width: 100%; border-collapse: collapse;">' +
              '<tr><td style="padding: 8px 0; font-weight: bold;">Name:</td><td style="padding: 8px 0;">' + (animal.name || 'Unknown') + '</td></tr>' +
              '<tr><td style="padding: 8px 0; font-weight: bold;">Species:</td><td style="padding: 8px 0;">' + (animal.species || 'Unknown') + '</td></tr>' +
              '<tr><td style="padding: 8px 0; font-weight: bold;">Age:</td><td style="padding: 8px 0;">' + (animal.age || 'Unknown') + '</td></tr>' +
              '<tr><td style="padding: 8px 0; font-weight: bold;">Group:</td><td style="padding: 8px 0;">' + (animal.Group || animal.group || 'Unknown') + '</td></tr>' +
              '<tr><td style="padding: 8px 0; font-weight: bold;">Status:</td><td style="padding: 8px 0;"><span style="background: #4CAF50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">' + (animal.status || 'Unknown') + '</span></td></tr>' +
              '<tr><td style="padding: 8px 0; font-weight: bold;">Notes:</td><td style="padding: 8px 0;">' + (animal.notes || 'No notes available') + '</td></tr>' +
            '</table>' +
          '</div>' +
          (animal.photo_url ? '<div style="flex: 0 0 200px;"><img src="' + animal.photo_url + '" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; border: 2px solid #ddd;" alt="' + (animal.name || 'Animal') + ' Photo"></div>' : '') +
        '</div>' +
      '</div>';

      // Daily Logs Section
      if (sections.includes('logs') && data.logs && data.logs.length > 0) {
        html += '<div style="margin-bottom: 30px; page-break-inside: avoid;">' +
          '<h3 style="color: #4CAF50; border-bottom: 1px solid #ddd; padding-bottom: 10px;">Daily Logs</h3>';

        data.logs.forEach(log => {
          console.log('Processing log:', log); // Debug log structure
          const logDate = new Date(log.created_on || log.created_at).toLocaleDateString('en-GB');

          // Build log content from available fields
          let logContent = '';

          // Handle fed status (stored as text 'true'/'false')
          if (log.fed) {
            const fedStatus = log.fed === 'true' || log.fed === true ? 'Yes' : 'No';
            logContent += '<strong>Fed:</strong> ' + fedStatus + '<br>';
          }

          // Handle cleaned status (stored as text 'true'/'false')
          if (log.cleaned) {
            const cleanedStatus = log.cleaned === 'true' || log.cleaned === true ? 'Yes' : 'No';
            logContent += '<strong>Cleaned:</strong> ' + cleanedStatus + '<br>';
          }

          // Handle medication
          if (log.medication && log.medication.trim()) {
            logContent += '<strong>Medication:</strong> ' + log.medication + '<br>';
          }

          // Handle notes
          if (log.notes && log.notes.trim()) {
            logContent += '<strong>Notes:</strong> ' + log.notes + '<br>';
          }

          // Handle logged by
          if (log.logged_by && log.logged_by.trim()) {
            logContent += '<strong>Logged by:</strong> ' + log.logged_by + '<br>';
          }

          // If no content found, show available fields for debugging
          if (!logContent) {
            logContent = 'Available fields: ' + Object.keys(log).join(', ');
          }

          html += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #eee; border-radius: 8px; background: #f9f9f9;">' +
            '<div style="font-weight: bold; color: #4CAF50; margin-bottom: 8px;">' + logDate + '</div>' +
            '<div style="line-height: 1.5; font-size: 12px;">' + (logContent || 'No entry') + '</div>' +
          '</div>';
        });

        html += '</div>';
      }

      // Medical Episodes Section
      if (sections.includes('medical') && data.medical && data.medical.length > 0) {
        html += '<div style="margin-bottom: 30px; page-break-inside: avoid;">' +
          '<h3 style="color: #4CAF50; border-bottom: 1px solid #ddd; padding-bottom: 10px;">Medical Episodes & Interventions</h3>';

        data.medical.forEach(episode => {
          const episodeDate = new Date(episode.created_at).toLocaleDateString('en-GB');
          html += '<div style="margin: 20px 0; padding: 15px; border: 1px solid #eee; border-radius: 8px; background: #f9f9f9;">' +
            '<div style="font-weight: bold; color: #4CAF50; margin-bottom: 8px;">Episode: ' + (episode.episode_name || 'Unnamed') + ' (' + episodeDate + ')</div>' +
            '<div style="margin-bottom: 10px;"><strong>Description:</strong> ' + (episode.description || 'No description') + '</div>' +
            '<div style="margin-bottom: 10px;"><strong>Status:</strong> ' + (episode.status || 'Unknown') + '</div>' +
            ((episode.medical_interventions || episode.interventions) && (episode.medical_interventions || episode.interventions).length > 0 ?
              '<div style="margin-top: 15px;">' +
                '<strong>Interventions:</strong>' +
                (episode.medical_interventions || episode.interventions).map(intervention =>
                  '<div style="margin: 10px 0 10px 20px; padding: 10px; border-left: 3px solid #4CAF50; background: white;">' +
                    '<div style="font-weight: bold;">' + (intervention.intervention_type || 'Unknown Type') + '</div>' +
                    '<div style="margin: 5px 0;"><strong>Date:</strong> ' + new Date(intervention.created_at).toLocaleDateString('en-GB') + '</div>' +
                    '<div style="margin: 5px 0;"><strong>Notes:</strong> ' + (intervention.notes || 'No notes') + '</div>' +
                    (intervention.outcome ? '<div style="margin: 5px 0;"><strong>Outcome:</strong> ' + intervention.outcome + '</div>' : '') +
                  '</div>'
                ).join('') +
              '</div>'
            : '') +
          '</div>';
        });

        html += '</div>';
      }

      // Adoption History Section
      if (sections.includes('adoption') && data.adoption && data.adoption.length > 0) {
        html += '<div style="margin-bottom: 30px; page-break-inside: avoid;">' +
          '<h3 style="color: #4CAF50; border-bottom: 1px solid #ddd; padding-bottom: 10px;">Adoption History</h3>';

        data.adoption.forEach(adoption => {
          const adoptionDate = new Date(adoption.created_at).toLocaleDateString('en-GB');
          const startDate = new Date(adoption.start_date).toLocaleDateString('en-GB');
          const endDate = new Date(adoption.end_date).toLocaleDateString('en-GB');
          html += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #eee; border-radius: 8px; background: #f9f9f9;">' +
            '<div style="font-weight: bold; color: #4CAF50; margin-bottom: 8px;">Adoption Record: ' + adoptionDate + '</div>' +
            '<div style="margin: 5px 0;"><strong>Adopter:</strong> ' + (adoption.donor_name || 'Unknown') + '</div>' +
            '<div style="margin: 5px 0;"><strong>Email:</strong> ' + (adoption.donor_email || 'Not provided') + '</div>' +
            '<div style="margin: 5px 0;"><strong>Amount:</strong> £' + (adoption.amount || '0.00') + '</div>' +
            '<div style="margin: 5px 0;"><strong>Period:</strong> ' + startDate + ' to ' + endDate + '</div>' +
            (adoption.notes ? '<div style="margin: 5px 0;"><strong>Notes:</strong> ' + adoption.notes + '</div>' : '') +
          '</div>';
        });

        html += '</div>';
      }

      // Donation History Section
      if (sections.includes('donation') && data.donation && data.donation.length > 0) {
        html += '<div style="margin-bottom: 30px; page-break-inside: avoid;">' +
          '<h3 style="color: #4CAF50; border-bottom: 1px solid #ddd; padding-bottom: 10px;">Donation History</h3>';

        data.donation.forEach(donation => {
          const donationDate = new Date(donation.created_at).toLocaleDateString('en-GB');
          const startDate = new Date(donation.start_date).toLocaleDateString('en-GB');
          const endDate = new Date(donation.end_date).toLocaleDateString('en-GB');
          html += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #eee; border-radius: 8px; background: #f9f9f9;">' +
            '<div style="font-weight: bold; color: #4CAF50; margin-bottom: 8px;">Donation Record: ' + donationDate + '</div>' +
            '<div style="margin: 5px 0;"><strong>Amount:</strong> £' + (donation.amount || '0.00') + '</div>' +
            '<div style="margin: 5px 0;"><strong>Donor:</strong> ' + (donation.donor_name || 'Anonymous') + '</div>' +
            '<div style="margin: 5px 0;"><strong>Email:</strong> ' + (donation.donor_email || 'Not provided') + '</div>' +
            '<div style="margin: 5px 0;"><strong>Period:</strong> ' + startDate + ' to ' + endDate + '</div>' +
            (donation.notes ? '<div style="margin: 5px 0;"><strong>Notes:</strong> ' + donation.notes + '</div>' : '') +
          '</div>';
        });

        html += '</div>';
      }

      html += '<div style="margin-top: 40px; text-align: center; color: #888; font-size: 12px; border-top: 1px solid #ddd; padding-top: 20px;">' +
        '<p>This report was generated by the Cornish Birds of Prey Center Management System</p>' +
        '<p>Report generated on ' + currentDate + ' for ' + (animal.name || 'Unknown Animal') + '</p>' +
        '</div>' +
        '</div>';

      return html;
    }

    // PDF generation is now handled in the preview window

    // Test if html2pdf is available
    if (typeof html2pdf === 'undefined') {
      console.error('html2pdf not found — check your CDN or script load order!');
      alert('html2pdf library not loaded properly. Please refresh the page.');
    } else {
      console.log('html2pdf library loaded successfully');
    }

    // Simple test function for debugging
    window.testPDF = function() {
      console.log('Testing basic PDF generation...');
      const element = document.createElement('div');
      element.innerHTML = `<h1>Hello PDF</h1><p>This is a test</p><p>Current time: ${new Date().toLocaleString()}</p>`;
      element.style.padding = '20px';
      element.style.fontFamily = 'Arial, sans-serif';
      document.body.appendChild(element);

      html2pdf().from(element).save('test.pdf').then(() => {
        console.log('Test PDF generated successfully');
        document.body.removeChild(element);
      }).catch(error => {
        console.error('Test PDF failed:', error);
        document.body.removeChild(element);
      });
    };

    // Initialize the app
    initializeApp();
    window.closeEditModal = closeEditModal;
    window.openReportModal = openReportModal;
    window.closeReportModal = closeReportModal;
    window.generateReport = generateReport;
  </script>
</body>
</html>