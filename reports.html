<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Reports – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Generate Report</h1>
    </header>

    <main class="dashboard">
      <section class="report-type-buttons">
        <button class="report-type-btn active" data-type="animal">Animal Report</button>
      </section>

      <section class="animal-search-section">
        <label for="animal-search">Select Animal:</label>
        <input type="text" id="animal-search" placeholder="Search by name..." />
        <div id="animal-results" class="animal-results"></div>
      </section>

      <section class="report-options">
        <h3>Include in Report:</h3>
        <label><input type="checkbox" class="section-checkbox" value="logs" checked /> Daily Logs</label>
        <label><input type="checkbox" class="section-checkbox" value="medical" /> Medical Episodes</label>
        <label><input type="checkbox" class="section-checkbox" value="adoptions" /> Adoptions & Donations</label>
      </section>

      <section class="report-action">
        <button class="btn-primary" id="generate-report-btn">
          <span class="material-icons">description</span>
          Generate Report
        </button>
      </section>
    </main>
  </div>

  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    const searchInput = document.getElementById('animal-search');
    const resultsContainer = document.getElementById('animal-results');
    let selectedAnimal = null;

    searchInput.addEventListener('input', async () => {
      const searchTerm = searchInput.value.trim().toLowerCase();
      if (searchTerm.length < 2) return (resultsContainer.innerHTML = '');

      const { data, error } = await supabase
        .from('animals')
        .select('*')
        .ilike('name', `%${searchTerm}%`)
        .limit(10);

      if (error) return (resultsContainer.innerHTML = '<p>Error loading animals.</p>');
      if (data.length === 0) return (resultsContainer.innerHTML = '<p>No animals found.</p>');

      resultsContainer.innerHTML = data.map(animal => `
        <div class="report-animal-card" data-id="${animal.id}">
          <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="${animal.name}" />
          <div>
            <strong>${animal.name}</strong><br>
            <small>${animal.species || 'Unknown Species'}</small>
          </div>
        </div>
      `).join('');

      document.querySelectorAll('.report-animal-card').forEach(card => {
        card.addEventListener('click', () => {
          document.querySelectorAll('.report-animal-card').forEach(c => c.classList.remove('selected'));
          card.classList.add('selected');
          selectedAnimal = data.find(a => a.id === card.dataset.id);
        });
      });
    });

    document.getElementById('generate-report-btn').addEventListener('click', () => {
      if (!selectedAnimal) return alert('Please select an animal to generate the report.');

      const sections = Array.from(document.querySelectorAll('.section-checkbox:checked')).map(cb => cb.value);
      const reportWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes');
      const today = new Date().toLocaleDateString('en-GB');
      const filename = `${selectedAnimal.name.replace(/[^a-zA-Z0-9]/g, '_')}_Report_${today.replace(/\//g, '-')}.pdf`;

      const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Animal Report – ${selectedAnimal.name}</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 2rem; background: #fff; color: #333; }
    .report-header { border-bottom: 2px solid #4CAF50; margin-bottom: 2rem; }
    .report-header h1 { color: #4CAF50; margin: 0; }
    .report-section { margin-bottom: 2rem; }
    .photo { max-width: 200px; border-radius: 8px; border: 2px solid #ddd; }
    .profile-table td { padding: 0.5rem; vertical-align: top; }
    .floating-buttons { position: fixed; top: 1rem; right: 1rem; z-index: 999; display: flex; gap: 10px; }
    .floating-buttons button { background: #4CAF50; border: none; padding: 0.6rem 1rem; color: white; font-weight: bold; border-radius: 5px; cursor: pointer; }
    .floating-buttons button.pdf { background: #2196F3; }
    @media print { .floating-buttons { display: none !important; } }
  </style>
</head>
<body>
  <div class="floating-buttons">
    <button onclick="window.print()">🖰 Print</button>
    <button class="pdf" onclick="saveToPDF()">📄 Save as PDF</button>
  </div>
  <div class="report-header">
    <h1>Animal Report</h1>
    <p>Generated on ${today}</p>
  </div>
  <div class="report-section">
    <h2>Animal Profile</h2>
    <table class="profile-table">
      <tr><td><strong>Name:</strong></td><td>${selectedAnimal.name}</td></tr>
      <tr><td><strong>Species:</strong></td><td>${selectedAnimal.species || '—'}</td></tr>
      <tr><td><strong>Group:</strong></td><td>${selectedAnimal.group || selectedAnimal.Group || '—'}</td></tr>
      <tr><td><strong>Status:</strong></td><td>${selectedAnimal.status || '—'}</td></tr>
      <tr><td><strong>Age:</strong></td><td>${selectedAnimal.age || '—'}</td></tr>
      <tr><td><strong>Notes:</strong></td><td>${selectedAnimal.notes || '—'}</td></tr>
    </table>
    ${selectedAnimal.photo_url ? `<img class="photo" src="${selectedAnimal.photo_url}" alt="Animal photo" />` : ''}
  </div>
  <script>
    function saveToPDF() {
      const element = document.body;
      html2pdf().from(element).set({
        margin: 0.5,
        filename: "${filename}",
        html2canvas: { scale: 0.8 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
      }).save();
    }
  </script>
</body>
</html>
`;

      reportWindow.document.write(html);
      reportWindow.document.close();
    });
  </script>
</body>
</html>