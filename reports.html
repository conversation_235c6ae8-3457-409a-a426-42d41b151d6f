<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Reports – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Generate Report</h1>
    </header>

    <main class="dashboard">
      <section class="report-type-buttons">
        <button class="report-type-btn active" data-type="animal">Animal Report</button>
      </section>

      <section class="animal-search-section">
        <label for="animal-search">Select Animal:</label>
        <input type="text" id="animal-search" placeholder="Search by name..." />
        <div id="animal-results" class="animal-results"></div>
      </section>

      <section class="report-options">
        <h3>Include in Report:</h3>
        <label><input type="checkbox" class="section-checkbox" value="logs" checked /> Daily Logs</label>
        <label><input type="checkbox" class="section-checkbox" value="medical" /> Medical Episodes</label>
        <label><input type="checkbox" class="section-checkbox" value="adoptions" /> Adoptions & Donations</label>
      </section>

      <section class="report-action">
        <button class="btn-primary" id="generate-report-btn">
          <span class="material-icons">description</span>
          Generate Report
        </button>
      </section>
    </main>
  </div>

  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    const searchInput = document.getElementById('animal-search');
    const resultsContainer = document.getElementById('animal-results');
    let selectedAnimal = null;

    searchInput.addEventListener('input', async () => {
      const searchTerm = searchInput.value.trim().toLowerCase();
      if (searchTerm.length < 2) {
        resultsContainer.innerHTML = '';
        return;
      }

      const { data, error } = await supabase
        .from('animals')
        .select('id, name, species, group, photo_url, status, age, notes')
        .ilike('name', `%${searchTerm}%`)
        .limit(10);

      if (error) {
        resultsContainer.innerHTML = '<p>Error loading animals.</p>';
        console.error(error);
        return;
      }

      if (!data.length) {
        resultsContainer.innerHTML = '<p>No animals found.</p>';
        return;
      }

      resultsContainer.innerHTML = data.map(animal => `
        <div class="report-animal-card" data-id="${animal.id}">
          <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="${animal.name}" />
          <div>
            <strong>${animal.name}</strong><br>
            <small>${animal.species || 'Unknown Species'}</small>
          </div>
        </div>
      `).join('');

      document.querySelectorAll('.report-animal-card').forEach(card => {
        card.addEventListener('click', () => {
          document.querySelectorAll('.report-animal-card').forEach(c => c.classList.remove('selected'));
          card.classList.add('selected');
          const id = card.getAttribute('data-id');
          selectedAnimal = data.find(a => a.id.toString() === id);
        });
      });
    });

    document.getElementById('generate-report-btn').addEventListener('click', () => {
      if (!selectedAnimal) {
        alert('Please select an animal to generate the report.');
        return;
      }

      const sections = Array.from(document.querySelectorAll('.section-checkbox:checked')).map(cb => cb.value);
      const today = new Date().toLocaleDateString('en-GB');
      const filename = `${selectedAnimal.name.replace(/[^a-zA-Z0-9]/g, '_')}_Report_${today.replace(/\//g, '-')}.pdf`;

      const reportWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes');
      const doc = reportWindow.document;
      doc.open();
      doc.write('<!DOCTYPE html><html><head><title>Animal Report</title></head><body></body></html>');
      doc.close();

      const style = doc.createElement('style');
      style.textContent = `
        body { font-family: Arial, sans-serif; padding: 2rem; color: #333; background: white; }
        .report-header { border-bottom: 2px solid #4CAF50; margin-bottom: 2rem; }
        .report-header h1 { margin: 0; color: #4CAF50; }
        .floating-buttons {
          position: fixed;
          top: 1rem;
          right: 1rem;
          z-index: 999;
          display: flex;
          gap: 10px;
        }
        .floating-buttons button {
          background: #4CAF50;
          border: none;
          padding: 0.6rem 1rem;
          color: white;
          font-weight: bold;
          border-radius: 5px;
          cursor: pointer;
        }
        .floating-buttons button.pdf { background: #2196F3; }
        @media print { .floating-buttons { display: none !important; } }
        .profile-table td { padding: 0.5rem; vertical-align: top; }
        .photo {
          max-width: 200px;
          border-radius: 8px;
          border: 2px solid #ddd;
          margin-top: 1rem;
        }
      `;
      doc.head.appendChild(style);

      const floatBtns = doc.createElement('div');
      floatBtns.className = 'floating-buttons';
      floatBtns.innerHTML = `
        <button onclick="window.print()">🖨️ Print</button>
        <button class="pdf" id="pdf-btn">📄 Save as PDF</button>
      `;
      doc.body.appendChild(floatBtns);

      const header = doc.createElement('div');
      header.className = 'report-header';
      header.innerHTML = `
        <h1>Animal Report</h1>
        <p>Generated on ${today}</p>
      `;
      doc.body.appendChild(header);

      const section = doc.createElement('div');
      section.className = 'report-section';
      section.innerHTML = `
        <h2>Animal Profile</h2>
        <table class="profile-table">
          <tr><td><strong>Name:</strong></td><td>${selectedAnimal.name}</td></tr>
          <tr><td><strong>Species:</strong></td><td>${selectedAnimal.species || '—'}</td></tr>
          <tr><td><strong>Group:</strong></td><td>${selectedAnimal.group || selectedAnimal.Group || '—'}</td></tr>
          <tr><td><strong>Status:</strong></td><td>${selectedAnimal.status || '—'}</td></tr>
          <tr><td><strong>Age:</strong></td><td>${selectedAnimal.age || '—'}</td></tr>
          <tr><td><strong>Notes:</strong></td><td>${selectedAnimal.notes || '—'}</td></tr>
        </table>
        ${selectedAnimal.photo_url ? `<img class="photo" src="${selectedAnimal.photo_url}" alt="Animal photo" />` : ''}
      `;
      doc.body.appendChild(section);

      const pdfScript = doc.createElement('script');
      pdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js';
      pdfScript.onload = () => {
        const pdfBtn = doc.getElementById('pdf-btn');
        pdfBtn.addEventListener('click', () => {
          html2pdf().from(doc.body).set({
            margin: 0.5,
            filename: filename,
            html2canvas: { scale: 0.8 },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
          }).save();
        });
      };
      doc.body.appendChild(pdfScript);
    });
  </script>
</body>
</html>
