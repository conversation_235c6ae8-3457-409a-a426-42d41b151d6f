<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Reports – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Generate Report</h1>
    </header>

    <main class="dashboard">
      <section class="report-type-buttons">
        <button class="report-type-btn active" data-type="animal">
          <span class="material-icons">pets</span>
          Animal Report
        </button>
        <button class="report-type-btn" data-type="daily-logs">
          <span class="material-icons">assignment</span>
          Daily Logs Report
        </button>
        <button class="report-type-btn" data-type="staff-volunteers">
          <span class="material-icons">people</span>
          Staff & Volunteers Report
        </button>
        <button class="report-type-btn" data-type="adoptions-donations">
          <span class="material-icons">volunteer_activism</span>
          Adoptions & Donations Report
        </button>
      </section>

      <!-- Animal Report Builder -->
      <section class="report-builder animal-report-builder" id="animal-report-builder">
        <div class="search-section">
          <label for="animal-search">Select Animal:</label>
          <input type="text" id="animal-search" class="search-input" placeholder="Search by name..." />
          <div id="animal-results" class="animal-results"></div>
        </div>

        <div class="report-options">
          <h3>Include in Report:</h3>
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" class="section-checkbox" value="logs" checked />
              <span class="checkmark"></span>
              Daily Logs
            </label>
            <label class="checkbox-label">
              <input type="checkbox" class="section-checkbox" value="medical" />
              <span class="checkmark"></span>
              Medical Episodes
            </label>
            <label class="checkbox-label">
              <input type="checkbox" class="section-checkbox" value="adoptions" />
              <span class="checkmark"></span>
              Adoptions & Donations
            </label>
            <label class="checkbox-label">
              <input type="checkbox" id="full-history" />
              <span class="checkmark"></span>
              Full History
            </label>
          </div>
        </div>

        <div class="date-range-section" id="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="start-date">Start Date:</label>
              <input type="date" id="start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="end-date">End Date:</label>
              <input type="date" id="end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <!-- Daily Logs Report Builder -->
      <section class="report-builder daily-logs-report-builder" id="daily-logs-report-builder" style="display: none;">
        <div class="report-options">
          <h3>Report Options:</h3>
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" class="logs-option" value="feeding" checked />
              <span class="checkmark"></span>
              Feeding Status
            </label>
            <label class="checkbox-label">
              <input type="checkbox" class="logs-option" value="cleaning" checked />
              <span class="checkmark"></span>
              Cleaning Status
            </label>
            <label class="checkbox-label">
              <input type="checkbox" class="logs-option" value="staff-attribution" checked />
              <span class="checkmark"></span>
              Staff Attribution
            </label>
            <label class="checkbox-label">
              <input type="checkbox" class="logs-option" value="notes" checked />
              <span class="checkmark"></span>
              Notes
            </label>
          </div>
        </div>

        <div class="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="logs-start-date">Start Date:</label>
              <input type="date" id="logs-start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="logs-end-date">End Date:</label>
              <input type="date" id="logs-end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <!-- Staff & Volunteers Report Builder -->
      <section class="report-builder staff-volunteers-report-builder" id="staff-volunteers-report-builder" style="display: none;">
        <div class="filter-section">
          <h3>Filter Options:</h3>
          <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="current-staff">Current Staff</button>
            <button class="filter-btn" data-filter="current-volunteers">Current Volunteers</button>
            <button class="filter-btn" data-filter="historical">Historical Data</button>
          </div>
        </div>

        <div class="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="staff-start-date">Start Date:</label>
              <input type="date" id="staff-start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="staff-end-date">End Date:</label>
              <input type="date" id="staff-end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <!-- Adoptions & Donations Report Builder -->
      <section class="report-builder adoptions-donations-report-builder" id="adoptions-donations-report-builder" style="display: none;">
        <div class="filter-section">
          <h3>Filter Options:</h3>
          <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All Records</button>
            <button class="filter-btn" data-filter="expiring-1">Expiring in 1 Month</button>
            <button class="filter-btn" data-filter="expiring-3">Expiring in 3 Months</button>
            <button class="filter-btn" data-filter="expiring-6">Expiring in 6 Months</button>
          </div>
        </div>

        <div class="animal-filter-section">
          <label for="adoption-animal-search">Filter by Animal (Optional):</label>
          <input type="text" id="adoption-animal-search" class="search-input" placeholder="Search by animal name..." />
          <div id="adoption-animal-results" class="animal-results"></div>
        </div>

        <div class="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="adoption-start-date">Start Date:</label>
              <input type="date" id="adoption-start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="adoption-end-date">End Date:</label>
              <input type="date" id="adoption-end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <section class="report-action">
        <button class="btn-primary" id="generate-report-btn">
          <span class="material-icons">description</span>
          Generate Report
        </button>
      </section>
    </main>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="index.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Document Management</a></li>
      <li><a href="#" onclick="alert('Please select an animal from the Dashboard to access the Medical Tracker')"><span class="material-icons">medical_services</span> Medical Tracker</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff/Volunteer Module</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Donations and Adoptions Module</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts Module</a></li>
      <li class="active"><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
    </ul>
  </nav>

  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    const searchInput = document.getElementById('animal-search');
    const resultsContainer = document.getElementById('animal-results');
    let selectedAnimal = null;
    let currentReportType = 'animal';

    window.toggleMenu = function() {
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (!sideMenu || !overlay) {
        console.error('Menu elements not found:', { sideMenu, overlay });
        return;
      }

      const isOpen = sideMenu.classList.contains('open');

      if (isOpen) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
      }
    };

    // Report type switching functionality
    function switchReportType(type) {
      currentReportType = type;

      // Update button states
      document.querySelectorAll('.report-type-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-type="${type}"]`).classList.add('active');

      // Hide all report builders
      document.querySelectorAll('.report-builder').forEach(builder => {
        builder.style.display = 'none';
      });

      // Show selected report builder
      const targetBuilder = document.getElementById(`${type}-report-builder`);
      if (targetBuilder) {
        targetBuilder.style.display = 'block';
      }

      // Set default dates for the current report type
      setDefaultDates(type);
    }

    // Set default dates based on report type
    function setDefaultDates(type) {
      const today = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(today.getDate() - 30);

      const todayStr = today.toISOString().split('T')[0];
      const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0];

      switch(type) {
        case 'animal':
          document.getElementById('start-date').value = thirtyDaysAgoStr;
          document.getElementById('end-date').value = todayStr;
          break;
        case 'daily-logs':
          document.getElementById('logs-start-date').value = thirtyDaysAgoStr;
          document.getElementById('logs-end-date').value = todayStr;
          break;
        case 'staff-volunteers':
          document.getElementById('staff-start-date').value = thirtyDaysAgoStr;
          document.getElementById('staff-end-date').value = todayStr;
          break;
        case 'adoptions-donations':
          document.getElementById('adoption-start-date').value = thirtyDaysAgoStr;
          document.getElementById('adoption-end-date').value = todayStr;
          break;
      }
    }

    // Initialize report type buttons
    document.querySelectorAll('.report-type-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const type = btn.getAttribute('data-type');
        switchReportType(type);
      });
    });

    // Handle full history checkbox
    document.getElementById('full-history').addEventListener('change', function() {
      const dateRangeSection = document.getElementById('date-range-section');
      if (this.checked) {
        dateRangeSection.style.display = 'none';
      } else {
        dateRangeSection.style.display = 'block';
      }
    });

    // Initialize filter buttons for different report types
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        // Remove active class from siblings
        this.parentElement.querySelectorAll('.filter-btn').forEach(sibling => {
          sibling.classList.remove('active');
        });
        // Add active class to clicked button
        this.classList.add('active');
      });
    });

    // Initialize default report type and dates
    switchReportType('animal');

    searchInput.addEventListener('input', async () => {
      const searchTerm = searchInput.value.trim().toLowerCase();
      if (searchTerm.length < 2) {
        resultsContainer.innerHTML = '';
        return;
      }

      const { data, error } = await supabase
        .from('animals')
        .select('id, name, species, Group, photo_url, status, Age, notes')
        .ilike('name', `%${searchTerm}%`)
        .limit(10);

      if (error) {
        resultsContainer.innerHTML = '<p>Error loading animals.</p>';
        console.error(error);
        return;
      }

      if (!data.length) {
        resultsContainer.innerHTML = '<p>No animals found.</p>';
        return;
      }

      resultsContainer.innerHTML = data.map(animal => `
        <div class="report-animal-card" data-id="${animal.id}">
          <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="${animal.name}" />
          <div>
            <strong>${animal.name}</strong><br>
            <small>${animal.species || 'Unknown Species'} - ${animal.Group || 'No Group'}</small>
          </div>
        </div>
      `).join('');

      document.querySelectorAll('.report-animal-card').forEach(card => {
        card.addEventListener('click', () => {
          // Remove selected class from all cards
          document.querySelectorAll('.report-animal-card').forEach(c => c.classList.remove('selected'));
          // Add selected class to clicked card
          card.classList.add('selected');
          const id = card.getAttribute('data-id');
          selectedAnimal = data.find(a => String(a.id) === String(id));
          console.log('Selected:', selectedAnimal);
        });
      });
    });

    document.getElementById('generate-report-btn').addEventListener('click', () => {
      switch(currentReportType) {
        case 'animal':
          generateAnimalReport();
          break;
        case 'daily-logs':
          generateDailyLogsReport();
          break;
        case 'staff-volunteers':
          generateStaffVolunteersReport();
          break;
        case 'adoptions-donations':
          generateAdoptionsDonationsReport();
          break;
        default:
          alert('Please select a report type.');
      }
    });

    // Generate Animal Report
    function generateAnimalReport() {
      if (!selectedAnimal) {
        alert('Please select an animal to generate the report.');
        return;
      }

      const selectedSections = Array.from(document.querySelectorAll('.section-checkbox:checked')).map(cb => cb.value);
      const fullHistory = document.getElementById('full-history').checked;
      const startDate = document.getElementById('start-date').value;
      const endDate = document.getElementById('end-date').value;

      // Build URL parameters
      const params = new URLSearchParams({
        animalId: selectedAnimal.id,
        animalName: selectedAnimal.name,
        sections: selectedSections.join(','),
        fullHistory: fullHistory.toString(),
        startDate: fullHistory ? '' : startDate,
        endDate: fullHistory ? '' : endDate
      });

      // Open animal report in new window
      window.open(`animal-report.html?${params.toString()}`, '_blank');
    }

    // Generate Daily Logs Report
    function generateDailyLogsReport() {
      const selectedOptions = Array.from(document.querySelectorAll('.logs-option:checked')).map(cb => cb.value);
      const startDate = document.getElementById('logs-start-date').value;
      const endDate = document.getElementById('logs-end-date').value;

      if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
      }

      const params = new URLSearchParams({
        options: selectedOptions.join(','),
        startDate: startDate,
        endDate: endDate
      });

      // Open daily logs report in new window
      window.open(`daily-logs-report.html?${params.toString()}`, '_blank');
    }

    // Generate Staff & Volunteers Report
    function generateStaffVolunteersReport() {
      const activeFilter = document.querySelector('.staff-volunteers-report-builder .filter-btn.active');
      const filterType = activeFilter ? activeFilter.getAttribute('data-filter') : 'all';
      const startDate = document.getElementById('staff-start-date').value;
      const endDate = document.getElementById('staff-end-date').value;

      if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
      }

      const params = new URLSearchParams({
        filter: filterType,
        startDate: startDate,
        endDate: endDate
      });

      // Open staff volunteers report in new window
      window.open(`staff-volunteers-report.html?${params.toString()}`, '_blank');
    }

    // Generate Adoptions & Donations Report
    function generateAdoptionsDonationsReport() {
      const activeFilter = document.querySelector('.adoptions-donations-report-builder .filter-btn.active');
      const filterType = activeFilter ? activeFilter.getAttribute('data-filter') : 'all';
      const startDate = document.getElementById('adoption-start-date').value;
      const endDate = document.getElementById('adoption-end-date').value;

      if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
      }

      const params = new URLSearchParams({
        filter: filterType,
        startDate: startDate,
        endDate: endDate
      });

      // Open adoptions donations report in new window
      window.open(`adoptions-donations-report.html?${params.toString()}`, '_blank');
    }
  </script>
</body>
</html>
