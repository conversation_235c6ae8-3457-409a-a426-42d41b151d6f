<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Report Module - Cornish Birds of Prey Center</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <!-- Header -->
  <header class="header">
    <div class="header-content">
      <button class="hamburger-menu" onclick="toggleMenu()">
        <span></span>
        <span></span>
        <span></span>
      </button>
      <h1>Report Module</h1>
    </div>
  </header>

  <!-- Navigation Menu -->
  <nav class="nav-menu" id="navMenu">
    <div class="nav-header">
      <h3>Navigation</h3>
      <button class="close-menu" onclick="toggleMenu()">×</button>
    </div>
    <ul class="nav-links">
      <li><a href="dashboard.html"><i class="icon">🏠</i> Dashboard</a></li>
      <li><a href="documents.html"><i class="icon">📄</i> Document Management</a></li>
      <li><a href="medical-tracker.html"><i class="icon">🏥</i> Medical Tracker</a></li>
      <li><a href="staff-volunteers.html"><i class="icon">👥</i> Staff/Volunteer Module</a></li>
      <li><a href="donations-adoptions.html"><i class="icon">💝</i> Donations and Adoptions Module</a></li>
      <li><a href="emergency-contacts.html"><i class="icon">🚨</i> Emergency Contacts Module</a></li>
      <li><a href="reports.html" class="active"><i class="icon">📊</i> Report Module</a></li>
    </ul>
  </nav>

  <!-- Main Content -->
  <main class="main-content">
    <div class="container">
      <!-- Report Type Selector -->
      <section class="report-type-section">
        <h2>Select Report Type</h2>
        <div class="report-type-buttons">
          <button class="report-type-btn active" data-type="animal">
            <i class="icon">🦅</i>
            Animal Report
          </button>
          <!-- Future report types can be added here -->
        </div>
      </section>

      <!-- Animal Report Configuration -->
      <section class="animal-report-config" id="animalReportConfig">
        <!-- Animal Search -->
        <div class="search-section">
          <h3>Select Animal</h3>
          <div class="search-input-container">
            <input type="text" id="animalSearch" placeholder="Search animals by name..." class="search-input">
            <div class="search-results" id="searchResults"></div>
          </div>
          <div class="selected-animal" id="selectedAnimal" style="display: none;">
            <div class="selected-animal-card">
              <img id="selectedAnimalPhoto" src="" alt="Animal Photo" class="selected-animal-photo">
              <div class="selected-animal-info">
                <h4 id="selectedAnimalName"></h4>
                <p id="selectedAnimalSpecies"></p>
                <button class="change-animal-btn" onclick="clearSelectedAnimal()">Change Animal</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Report Sections -->
        <div class="report-sections" id="reportSections" style="display: none;">
          <h3>Report Sections</h3>
          
          <!-- General Info (Always Included) -->
          <div class="section-item always-included">
            <div class="section-toggle">
              <input type="checkbox" id="generalInfo" checked disabled>
              <label for="generalInfo" class="section-label">
                <i class="icon">ℹ️</i>
                General Information
                <span class="always-included-text">(Always Included)</span>
              </label>
            </div>
          </div>

          <!-- Optional Sections -->
          <div class="section-item">
            <div class="section-toggle">
              <input type="checkbox" id="dailyLogs">
              <label for="dailyLogs" class="section-label">
                <i class="icon">📝</i>
                Daily Logs
              </label>
            </div>
          </div>

          <div class="section-item">
            <div class="section-toggle">
              <input type="checkbox" id="medicalEpisodes">
              <label for="medicalEpisodes" class="section-label">
                <i class="icon">🏥</i>
                Medical Episodes
              </label>
            </div>
          </div>

          <div class="section-item">
            <div class="section-toggle">
              <input type="checkbox" id="adoptionsDonations">
              <label for="adoptionsDonations" class="section-label">
                <i class="icon">💝</i>
                Adoptions & Donations
              </label>
            </div>
          </div>
        </div>

        <!-- Generate Report Button -->
        <div class="generate-section" id="generateSection" style="display: none;">
          <button class="generate-report-btn" onclick="generateAnimalReport()">
            <i class="icon">📊</i>
            Generate Report
          </button>
        </div>
      </section>
    </div>
  </main>

  <script>
    // Supabase configuration
    const supabaseUrl = 'https://your-project-url.supabase.co';
    const supabaseKey = 'your-anon-key';
    const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

    let selectedAnimal = null;
    let searchTimeout = null;

    // Initialize the app
    function initializeApp() {
      setupEventListeners();
      loadSupabaseConfig();
    }

    function setupEventListeners() {
      const animalSearch = document.getElementById('animalSearch');
      animalSearch.addEventListener('input', handleAnimalSearch);
      
      // Report type buttons
      document.querySelectorAll('.report-type-btn').forEach(btn => {
        btn.addEventListener('click', handleReportTypeChange);
      });
    }

    async function loadSupabaseConfig() {
      try {
        const response = await fetch('supabase-config.json');
        if (response.ok) {
          const config = await response.json();
          window.supabase = window.supabase.createClient(config.url, config.key);
        }
      } catch (error) {
        console.error('Error loading Supabase config:', error);
      }
    }

    function handleReportTypeChange(event) {
      // Remove active class from all buttons
      document.querySelectorAll('.report-type-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      
      // Add active class to clicked button
      event.target.classList.add('active');
      
      // Show/hide relevant sections
      const reportType = event.target.dataset.type;
      if (reportType === 'animal') {
        document.getElementById('animalReportConfig').style.display = 'block';
      }
    }

    function handleAnimalSearch(event) {
      const query = event.target.value.trim();
      
      // Clear previous timeout
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
      
      // Debounce search
      searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
          searchAnimals(query);
        } else {
          clearSearchResults();
        }
      }, 300);
    }

    async function searchAnimals(query) {
      try {
        const { data, error } = await supabase
          .from('animals')
          .select('id, name, species, photo_url, status')
          .ilike('name', `%${query}%`)
          .eq('status', 'Active')
          .limit(10);

        if (error) throw error;
        
        displaySearchResults(data || []);
      } catch (error) {
        console.error('Error searching animals:', error);
        showToast('Error searching animals', 'error');
      }
    }

    function displaySearchResults(animals) {
      const resultsContainer = document.getElementById('searchResults');
      
      if (animals.length === 0) {
        resultsContainer.innerHTML = '<div class="no-results">No animals found</div>';
        resultsContainer.style.display = 'block';
        return;
      }
      
      const resultsHTML = animals.map(animal => `
        <div class="search-result-card" onclick="selectAnimal('${animal.id}')">
          <img src="${animal.photo_url || 'placeholder-animal.jpg'}" alt="${animal.name}" class="result-photo">
          <div class="result-info">
            <h4>${animal.name}</h4>
            <p>${animal.species}</p>
          </div>
        </div>
      `).join('');
      
      resultsContainer.innerHTML = resultsHTML;
      resultsContainer.style.display = 'block';
    }

    function clearSearchResults() {
      const resultsContainer = document.getElementById('searchResults');
      resultsContainer.style.display = 'none';
      resultsContainer.innerHTML = '';
    }

    async function selectAnimal(animalId) {
      try {
        const { data, error } = await supabase
          .from('animals')
          .select('*')
          .eq('id', animalId)
          .single();

        if (error) throw error;
        
        selectedAnimal = data;
        displaySelectedAnimal(data);
        clearSearchResults();
        
        // Show report sections and generate button
        document.getElementById('reportSections').style.display = 'block';
        document.getElementById('generateSection').style.display = 'block';
        
      } catch (error) {
        console.error('Error selecting animal:', error);
        showToast('Error selecting animal', 'error');
      }
    }

    function displaySelectedAnimal(animal) {
      document.getElementById('selectedAnimalPhoto').src = animal.photo_url || 'placeholder-animal.jpg';
      document.getElementById('selectedAnimalName').textContent = animal.name;
      document.getElementById('selectedAnimalSpecies').textContent = animal.species;
      
      document.getElementById('selectedAnimal').style.display = 'block';
      document.getElementById('animalSearch').style.display = 'none';
    }

    function clearSelectedAnimal() {
      selectedAnimal = null;
      document.getElementById('selectedAnimal').style.display = 'none';
      document.getElementById('animalSearch').style.display = 'block';
      document.getElementById('animalSearch').value = '';
      
      // Hide report sections
      document.getElementById('reportSections').style.display = 'none';
      document.getElementById('generateSection').style.display = 'none';
      
      // Reset checkboxes
      document.querySelectorAll('input[type="checkbox"]:not(#generalInfo)').forEach(checkbox => {
        checkbox.checked = false;
      });
    }

    // Navigation functions
    function toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      navMenu.classList.toggle('active');
    }

    function showToast(message, type = 'info') {
      // Toast notification implementation
      const toast = document.createElement('div');
      toast.className = `toast toast-${type}`;
      toast.textContent = message;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.classList.add('show');
      }, 100);
      
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }

    async function generateAnimalReport() {
      if (!selectedAnimal) {
        showToast('Please select an animal first', 'error');
        return;
      }

      try {
        // Get selected sections
        const sections = getSelectedSections();

        // Fetch data for selected sections
        const reportData = await fetchReportData(sections);

        // Generate HTML report
        const reportHTML = generateReportHTML(reportData, sections);

        // Open report in new window
        const reportWindow = window.open('', '_blank', 'width=800,height=600');
        reportWindow.document.write(reportHTML);
        reportWindow.document.close();

        showToast('Report generated successfully', 'success');

      } catch (error) {
        console.error('Error generating report:', error);
        showToast('Error generating report', 'error');
      }
    }

    function getSelectedSections() {
      const sections = ['general']; // Always include general info

      if (document.getElementById('dailyLogs').checked) {
        sections.push('logs');
      }
      if (document.getElementById('medicalEpisodes').checked) {
        sections.push('medical');
      }
      if (document.getElementById('adoptionsDonations').checked) {
        sections.push('adoptions', 'donations');
      }

      return sections;
    }

    async function fetchReportData(sections) {
      const data = { animal: selectedAnimal };

      for (const section of sections) {
        switch (section) {
          case 'logs':
            data.logs = await fetchDailyLogs();
            break;
          case 'medical':
            data.medical = await fetchMedicalData();
            break;
          case 'adoptions':
            data.adoptions = await fetchAdoptionsData();
            break;
          case 'donations':
            data.donations = await fetchDonationsData();
            break;
        }
      }

      return data;
    }

    async function fetchDailyLogs() {
      try {
        const { data, error } = await supabase
          .from('daily_logs')
          .select('*')
          .eq('animal_id', selectedAnimal.id)
          .order('created_on', { ascending: false })
          .limit(20);

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error fetching daily logs:', error);
        return [];
      }
    }

    async function fetchMedicalData() {
      try {
        const { data: episodes, error } = await supabase
          .from('medical_episodes')
          .select(`
            *,
            medical_interventions (*)
          `)
          .eq('animal_id', selectedAnimal.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        return episodes || [];
      } catch (error) {
        console.error('Error fetching medical data:', error);
        return [];
      }
    }

    async function fetchAdoptionsData() {
      try {
        const { data, error } = await supabase
          .from('adoptions_donations')
          .select('*')
          .eq('animal_id', selectedAnimal.id)
          .eq('donation_type', 'adoption')
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error fetching adoptions data:', error);
        return [];
      }
    }

    async function fetchDonationsData() {
      try {
        const { data, error } = await supabase
          .from('adoptions_donations')
          .select('*')
          .eq('animal_id', selectedAnimal.id)
          .eq('donation_type', 'donation')
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error fetching donations data:', error);
        return [];
      }
    }

    function generateReportHTML(data, sections) {
      const animal = data.animal;
      const currentDate = new Date().toLocaleDateString('en-GB');

      let html = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Animal Report - ${animal.name}</title>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              color: #333;
              background: white;
              line-height: 1.6;
            }
            .report-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #4CAF50;
              padding-bottom: 20px;
            }
            .report-header h1 {
              color: #4CAF50;
              margin: 0;
              font-size: 24px;
            }
            .report-header h2 {
              color: #666;
              margin: 10px 0;
              font-size: 18px;
            }
            .report-header p {
              margin: 5px 0;
              color: #888;
              font-size: 12px;
            }
            .section {
              margin-bottom: 30px;
              page-break-inside: avoid;
            }
            .section h3 {
              color: #4CAF50;
              border-bottom: 1px solid #ddd;
              padding-bottom: 10px;
              margin-bottom: 15px;
            }
            .animal-profile {
              display: flex;
              gap: 20px;
              margin-bottom: 20px;
            }
            .animal-photo {
              flex-shrink: 0;
            }
            .animal-photo img {
              width: 150px;
              height: 150px;
              object-fit: cover;
              border-radius: 8px;
              border: 2px solid #ddd;
            }
            .animal-details {
              flex: 1;
            }
            .animal-details table {
              width: 100%;
              border-collapse: collapse;
            }
            .animal-details td {
              padding: 8px 0;
              border-bottom: 1px solid #eee;
            }
            .animal-details td:first-child {
              font-weight: bold;
              width: 30%;
            }
            .log-item, .medical-item, .adoption-item {
              border: 1px solid #eee;
              border-radius: 8px;
              padding: 15px;
              margin-bottom: 15px;
              background: #f9f9f9;
            }
            .log-date, .medical-date, .adoption-date {
              font-weight: bold;
              color: #4CAF50;
              margin-bottom: 8px;
            }
            .intervention {
              margin: 10px 0 10px 20px;
              padding: 10px;
              border-left: 3px solid #4CAF50;
              background: white;
            }
            .floating-buttons {
              position: fixed;
              top: 20px;
              right: 20px;
              display: flex;
              gap: 10px;
              z-index: 1000;
            }
            .floating-btn {
              background: #4CAF50;
              color: white;
              border: none;
              padding: 12px 20px;
              border-radius: 25px;
              cursor: pointer;
              font-size: 14px;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              transition: all 0.3s ease;
            }
            .floating-btn:hover {
              background: #45a049;
              transform: translateY(-2px);
            }
            .report-footer {
              margin-top: 40px;
              text-align: center;
              color: #888;
              font-size: 12px;
              border-top: 1px solid #ddd;
              padding-top: 20px;
            }
            @media print {
              .floating-buttons {
                display: none !important;
              }
              body {
                padding: 0;
              }
            }
          </style>
        </head>
        <body>
          <div class="floating-buttons">
            <button class="floating-btn" onclick="window.print()">🖨️ Print</button>
            <button class="floating-btn" onclick="savePDF()">💾 Save as PDF</button>
          </div>

          <div class="report-header">
            <h1>Cornish Birds of Prey Center</h1>
            <h2>Animal Report</h2>
            <p>Generated on ${currentDate}</p>
          </div>`;

      // General Information Section (always included)
      if (sections.includes('general')) {
        html += `
          <div class="section">
            <h3>General Information</h3>
            <div class="animal-profile">
              ${animal.photo_url ? `
                <div class="animal-photo">
                  <img src="${animal.photo_url}" alt="${animal.name}" crossorigin="anonymous">
                </div>
              ` : ''}
              <div class="animal-details">
                <table>
                  <tr><td>Name:</td><td>${animal.name || 'Unknown'}</td></tr>
                  <tr><td>Species:</td><td>${animal.species || 'Unknown'}</td></tr>
                  <tr><td>Age:</td><td>${animal.age || 'Unknown'}</td></tr>
                  <tr><td>Group:</td><td>${animal.Group || animal.group || 'Unknown'}</td></tr>
                  <tr><td>Status:</td><td>${animal.status || 'Unknown'}</td></tr>
                  <tr><td>Arrival Date:</td><td>${animal.arrival_date ? new Date(animal.arrival_date).toLocaleDateString('en-GB') : 'Unknown'}</td></tr>
                  ${animal.notes ? `<tr><td>Notes:</td><td>${animal.notes}</td></tr>` : ''}
                </table>
              </div>
            </div>
          </div>`;
      }

      // Daily Logs Section
      if (sections.includes('logs') && data.logs && data.logs.length > 0) {
        html += `
          <div class="section">
            <h3>Daily Logs</h3>`;

        data.logs.forEach(log => {
          const logDate = new Date(log.created_on || log.created_at).toLocaleDateString('en-GB');
          let logContent = '';

          if (log.fed) {
            const fedStatus = log.fed === 'true' || log.fed === true ? 'Yes' : 'No';
            logContent += `<strong>Fed:</strong> ${fedStatus}<br>`;
          }
          if (log.cleaned) {
            const cleanedStatus = log.cleaned === 'true' || log.cleaned === true ? 'Yes' : 'No';
            logContent += `<strong>Cleaned:</strong> ${cleanedStatus}<br>`;
          }
          if (log.medication && log.medication.trim()) {
            logContent += `<strong>Medication:</strong> ${log.medication}<br>`;
          }
          if (log.notes && log.notes.trim()) {
            logContent += `<strong>Notes:</strong> ${log.notes}<br>`;
          }
          if (log.logged_by && log.logged_by.trim()) {
            logContent += `<strong>Logged by:</strong> ${log.logged_by}`;
          }

          html += `
            <div class="log-item">
              <div class="log-date">${logDate}</div>
              <div>${logContent || 'No entry recorded'}</div>
            </div>`;
        });

        html += `</div>`;
      }

      // Medical Episodes Section
      if (sections.includes('medical') && data.medical && data.medical.length > 0) {
        html += `
          <div class="section">
            <h3>Medical Episodes & Interventions</h3>`;

        data.medical.forEach(episode => {
          const episodeDate = new Date(episode.created_at).toLocaleDateString('en-GB');
          html += `
            <div class="medical-item">
              <div class="medical-date">Episode: ${episode.episode_name || 'Unnamed'} (${episodeDate})</div>
              <div><strong>Description:</strong> ${episode.description || 'No description'}</div>
              <div><strong>Status:</strong> ${episode.status || 'Unknown'}</div>`;

          if (episode.medical_interventions && episode.medical_interventions.length > 0) {
            html += `<div style="margin-top: 15px;"><strong>Interventions:</strong>`;
            episode.medical_interventions.forEach(intervention => {
              html += `
                <div class="intervention">
                  <div style="font-weight: bold;">${intervention.intervention_type || 'Unknown Type'}</div>
                  <div><strong>Date:</strong> ${new Date(intervention.created_at).toLocaleDateString('en-GB')}</div>
                  <div><strong>Notes:</strong> ${intervention.notes || 'No notes'}</div>
                  ${intervention.outcome ? `<div><strong>Outcome:</strong> ${intervention.outcome}</div>` : ''}
                </div>`;
            });
            html += `</div>`;
          }

          html += `</div>`;
        });

        html += `</div>`;
      }

      // Adoptions Section
      if (sections.includes('adoptions') && data.adoptions && data.adoptions.length > 0) {
        html += `
          <div class="section">
            <h3>Adoption History</h3>`;

        data.adoptions.forEach(adoption => {
          const adoptionDate = new Date(adoption.created_at).toLocaleDateString('en-GB');
          html += `
            <div class="adoption-item">
              <div class="adoption-date">Adoption Record: ${adoptionDate}</div>
              <div><strong>Adopter:</strong> ${adoption.donor_name || 'Unknown'}</div>
              <div><strong>Email:</strong> ${adoption.donor_email || 'Not provided'}</div>
              <div><strong>Amount:</strong> £${adoption.amount || '0.00'}</div>
              ${adoption.notes ? `<div><strong>Notes:</strong> ${adoption.notes}</div>` : ''}
            </div>`;
        });

        html += `</div>`;
      }

      // Donations Section
      if (sections.includes('donations') && data.donations && data.donations.length > 0) {
        html += `
          <div class="section">
            <h3>Donation History</h3>`;

        data.donations.forEach(donation => {
          const donationDate = new Date(donation.created_at).toLocaleDateString('en-GB');
          html += `
            <div class="adoption-item">
              <div class="adoption-date">Donation Record: ${donationDate}</div>
              <div><strong>Amount:</strong> £${donation.amount || '0.00'}</div>
              <div><strong>Donor:</strong> ${donation.donor_name || 'Anonymous'}</div>
              <div><strong>Email:</strong> ${donation.donor_email || 'Not provided'}</div>
              ${donation.notes ? `<div><strong>Notes:</strong> ${donation.notes}</div>` : ''}
            </div>`;
        });

        html += `</div>`;
      }

      html += `
          <div class="report-footer">
            <p>This report was generated by the Cornish Birds of Prey Center Management System</p>
            <p>Report generated on ${currentDate} for ${animal.name || 'Unknown Animal'}</p>
          </div>

          <script>
            function savePDF() {
              const element = document.body;
              const opt = {
                margin: 1,
                filename: 'animal-report-${animal.name?.replace(/[^a-zA-Z0-9]/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2, useCORS: true },
                jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
              };

              html2pdf().set(opt).from(element).save();
            }
          </script>
        </body>
        </html>`;

      return html;
    }

    // Initialize the app when DOM is loaded
    document.addEventListener('DOMContentLoaded', initializeApp);
  </script>
</body>
</html>
