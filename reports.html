<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Reports – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Generate Report</h1>
    </header>

    <main class="dashboard">
      <section class="report-type-buttons">
        <button class="report-type-btn active" data-type="animal">Animal Report</button>
      </section>

      <section class="animal-search-section">
        <label for="animal-search">Select Animal:</label>
        <input type="text" id="animal-search" placeholder="Search by name..." />
        <div id="animal-results" class="animal-results"></div>
      </section>

      <section class="report-options">
        <h3>Include in Report:</h3>
        <label><input type="checkbox" class="section-checkbox" value="logs" checked /> Daily Logs</label>
        <label><input type="checkbox" class="section-checkbox" value="medical" /> Medical Episodes</label>
        <label><input type="checkbox" class="section-checkbox" value="adoptions" /> Adoptions & Donations</label>
      </section>

      <section class="report-action">
        <button class="btn-primary" id="generate-report-btn">
          <span class="material-icons">description</span>
          Generate Report
        </button>
      </section>
    </main>
  </div>

  <!-- Navigation Menu -->
  <nav id="navMenu" class="nav-menu">
    <ul>
      <li><a href="index.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="documents.html"><span class="material-icons">folder</span> Document Management</a></li>
      <li><a href="medical-tracker.html"><span class="material-icons">medical_services</span> Medical Tracker</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff/Volunteer Module</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Donations and Adoptions Module</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts Module</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
    </ul>
  </nav>

  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Global functions for menu
    window.toggleMenu = function() {
      const navMenu = document.getElementById('navMenu');
      const menuOverlay = document.getElementById('menu-overlay');

      if (navMenu) {
        navMenu.classList.toggle('active');
      }
      if (menuOverlay) {
        menuOverlay.classList.toggle('active');
      }
    };

    console.log('Script loaded, initializing...');

    // Skip authentication for now to test basic functionality
    const searchInput = document.getElementById('animal-search');
    const resultsContainer = document.getElementById('animal-results');
    let selectedAnimal = null;

    console.log('Elements found:', { searchInput, resultsContainer });

    if (!searchInput || !resultsContainer) {
      console.error('Required elements not found!');
    }

    // Add simple search functionality
    searchInput.addEventListener('input', async () => {
      const searchTerm = searchInput.value.trim();
      console.log('Search input detected:', searchTerm);

      if (searchTerm.length < 2) {
        resultsContainer.innerHTML = '';
        console.log('Search term too short, clearing results');
        return;
      }

      console.log('Attempting database query...');

      try {
        const { data, error } = await supabase
          .from('animals')
          .select('id, name, species, group, photo_url, status, age, notes')
          .ilike('name', `%${searchTerm}%`)
          .limit(10);

        console.log('Database response:', { data, error });

        if (error) {
          resultsContainer.innerHTML = '<p style="color: red;">Database Error: ' + error.message + '</p>';
          console.error('Database error:', error);
          return;
        }

        if (!data || data.length === 0) {
          resultsContainer.innerHTML = '<p>No animals found for "' + searchTerm + '"</p>';
          console.log('No animals found');
          return;
        }

        console.log('Found', data.length, 'animals');

        resultsContainer.innerHTML = data.map(animal => `
          <div class="report-animal-card" data-id="${animal.id}" style="border: 1px solid #ccc; padding: 10px; margin: 5px; cursor: pointer;">
            <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="${animal.name}" style="width: 50px; height: 50px; object-fit: cover;" />
            <div>
              <strong>${animal.name}</strong><br>
              <small>${animal.species || 'Unknown Species'}</small>
            </div>
          </div>
        `).join('');

        // Add click handlers
        document.querySelectorAll('.report-animal-card').forEach(card => {
          card.addEventListener('click', () => {
            document.querySelectorAll('.report-animal-card').forEach(c => c.style.backgroundColor = '');
            card.style.backgroundColor = '#e3f2fd';
            const id = card.getAttribute('data-id');
            selectedAnimal = data.find(a => String(a.id) === String(id));
            console.log('Selected animal:', selectedAnimal);
          });
        });

      } catch (err) {
        console.error('Search error:', err);
        resultsContainer.innerHTML = '<p style="color: red;">Search failed: ' + err.message + '</p>';
      }
    });

      document.getElementById('generate-report-btn').addEventListener('click', () => {
        if (!selectedAnimal) {
          alert('Please select an animal to generate the report.');
          return;
        }

        const sections = Array.from(document.querySelectorAll('.section-checkbox:checked')).map(cb => cb.value);
        const today = new Date().toLocaleDateString('en-GB');
        const filename = `${selectedAnimal.name.replace(/[^a-zA-Z0-9]/g, '_')}_Report_${today.replace(/\//g, '-')}.pdf`;

        const reportWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes');
        const doc = reportWindow.document;
        doc.open();
        doc.write('<!DOCTYPE html><html><head><title>Animal Report</title></head><body></body></html>');
        doc.close();

        const style = doc.createElement('style');
        style.textContent = `
          body { font-family: Arial, sans-serif; padding: 2rem; color: #333; background: white; }
          .report-header { border-bottom: 2px solid #4CAF50; margin-bottom: 2rem; }
          .report-header h1 { margin: 0; color: #4CAF50; }
          .floating-buttons {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 999;
            display: flex;
            gap: 10px;
          }
          .floating-buttons button {
            background: #4CAF50;
            border: none;
            padding: 0.6rem 1rem;
            color: white;
            font-weight: bold;
            border-radius: 5px;
            cursor: pointer;
          }
          .floating-buttons button.pdf { background: #2196F3; }
          @media print { .floating-buttons { display: none !important; } }
          .profile-table td { padding: 0.5rem; vertical-align: top; }
          .photo {
            max-width: 200px;
            border-radius: 8px;
            border: 2px solid #ddd;
            margin-top: 1rem;
          }
        `;
        doc.head.appendChild(style);

        const floatBtns = doc.createElement('div');
        floatBtns.className = 'floating-buttons';
        floatBtns.innerHTML = `
          <button onclick="window.print()">🖨️ Print</button>
          <button class="pdf" id="pdf-btn">📄 Save as PDF</button>
        `;
        doc.body.appendChild(floatBtns);

        const header = doc.createElement('div');
        header.className = 'report-header';
        header.innerHTML = `
          <h1>Animal Report</h1>
          <p>Generated on ${today}</p>
        `;
        doc.body.appendChild(header);

        const section = doc.createElement('div');
        section.className = 'report-section';

        let photoHtml = '';
        if (selectedAnimal.photo_url) {
          photoHtml = '<img class="photo" src="' + selectedAnimal.photo_url + '" alt="Animal photo" />';
        }

        section.innerHTML = `
          <h2>Animal Profile</h2>
          <table class="profile-table">
            <tr><td><strong>Name:</strong></td><td>${selectedAnimal.name}</td></tr>
            <tr><td><strong>Species:</strong></td><td>${selectedAnimal.species || '—'}</td></tr>
            <tr><td><strong>Group:</strong></td><td>${selectedAnimal.group || selectedAnimal.Group || '—'}</td></tr>
            <tr><td><strong>Status:</strong></td><td>${selectedAnimal.status || '—'}</td></tr>
            <tr><td><strong>Age:</strong></td><td>${selectedAnimal.age || '—'}</td></tr>
            <tr><td><strong>Notes:</strong></td><td>${selectedAnimal.notes || '—'}</td></tr>
          </table>
          ${photoHtml}
        `;
        doc.body.appendChild(section);

        const pdfScript = doc.createElement('script');
        pdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js';
        pdfScript.onload = () => {
          const pdfBtn = doc.getElementById('pdf-btn');
          pdfBtn.addEventListener('click', () => {
            html2pdf().from(doc.body).set({
              margin: 0.5,
              filename: filename,
              html2canvas: { scale: 0.8 },
              jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
            }).save();
          });
        };
        doc.body.appendChild(pdfScript);
      });
  </script>
</body>
</html>
