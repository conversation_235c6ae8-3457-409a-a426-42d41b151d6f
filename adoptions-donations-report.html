<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Adoptions & Donations Report – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <style>
    /* Report-specific styles */
    .report-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      background: white;
      min-height: 100vh;
    }

    .report-header {
      text-align: center;
      margin-bottom: 2rem;
      border-bottom: 2px solid #2c3e50;
      padding-bottom: 1rem;
    }

    .report-title {
      color: #2c3e50;
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .report-subtitle {
      color: #666;
      font-size: 1.1rem;
    }

    .summary-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 1.5rem;
      text-align: center;
      border-left: 4px solid #4285a4;
    }

    .summary-number {
      font-size: 2rem;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 0.5rem;
    }

    .summary-label {
      color: #666;
      font-size: 0.9rem;
    }

    .records-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
    }

    .records-table th, .records-table td {
      border: 1px solid #ddd;
      padding: 0.75rem;
      text-align: left;
      vertical-align: top;
    }

    .records-table th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
      position: sticky;
      top: 0;
    }

    .records-table tr:nth-child(even) {
      background: #f9f9f9;
    }

    .animal-name {
      font-weight: 600;
      color: #2c3e50;
    }

    .record-type-adoption {
      background: #d4edda;
      color: #155724;
      padding: 0.25rem 0.5rem;
      border-radius: 15px;
      font-size: 0.85rem;
      font-weight: 500;
    }

    .record-type-donation {
      background: #d1ecf1;
      color: #0c5460;
      padding: 0.25rem 0.5rem;
      border-radius: 15px;
      font-size: 0.85rem;
      font-weight: 500;
    }

    .amount {
      font-weight: 600;
      color: #28a745;
    }

    .expiring-soon {
      background: #fff3cd;
      color: #856404;
    }

    .floating-buttons {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 10px;
      z-index: 1000;
    }

    .floating-btn {
      background: rgba(66, 133, 166, 0.9);
      color: white;
      border: none;
      border-radius: 50px;
      padding: 12px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .floating-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .no-data {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 2rem;
    }

    .loading {
      text-align: center;
      color: #666;
      padding: 2rem;
    }

    /* Print styles */
    @media print {
      .floating-buttons {
        display: none !important;
      }
      
      .report-container {
        padding: 0;
        box-shadow: none;
      }
      
      .summary-section {
        page-break-inside: avoid;
      }
      
      .records-table {
        page-break-inside: auto;
      }
      
      .records-table tr {
        page-break-inside: avoid;
      }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .report-container {
        padding: 1rem;
      }
      
      .summary-section {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .floating-buttons {
        position: relative;
        top: auto;
        right: auto;
        justify-content: center;
        margin-bottom: 1rem;
      }
      
      .records-table {
        font-size: 0.85rem;
      }
      
      .records-table th, .records-table td {
        padding: 0.5rem;
      }
    }
  </style>
</head>
<body>
  <!-- Floating Action Buttons -->
  <div class="floating-buttons">
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print
    </button>
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title">Adoptions & Donations Report</h1>
      <p class="report-subtitle" id="report-subtitle">Loading...</p>
    </div>

    <div class="summary-section" id="summary-section">
      <div class="summary-card">
        <div class="summary-number" id="total-records">-</div>
        <div class="summary-label">Total Records</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="total-adoptions">-</div>
        <div class="summary-label">Adoptions</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="total-donations">-</div>
        <div class="summary-label">Donations</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="total-amount">-</div>
        <div class="summary-label">Total Amount</div>
      </div>
    </div>

    <div id="records-content">
      <div class="loading">Loading adoptions and donations data...</div>
    </div>
  </div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const filter = urlParams.get('filter') || 'all';
    const startDate = urlParams.get('startDate');
    const endDate = urlParams.get('endDate');

    // Initialize report
    async function initializeReport() {
      if (!startDate || !endDate) {
        document.getElementById('records-content').innerHTML = '<div class="no-data">Invalid date range provided.</div>';
        return;
      }

      // Update report subtitle
      const filterText = getFilterText(filter);
      document.getElementById('report-subtitle').textContent = `${filterText} - ${startDate} to ${endDate}`;

      // Load adoptions and donations data
      await loadRecordsData();
    }

    // Get filter text for subtitle
    function getFilterText(filter) {
      switch(filter) {
        case 'expiring-1': return 'Expiring in 1 Month';
        case 'expiring-3': return 'Expiring in 3 Months';
        case 'expiring-6': return 'Expiring in 6 Months';
        default: return 'All Records';
      }
    }

    // Load adoptions and donations data
    async function loadRecordsData() {
      let query = supabase
        .from('adoptions_donations')
        .select(`
          *,
          animals (
            name,
            species,
            Group
          )
        `)
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .order('created_at', { ascending: false });

      // Apply expiration filters
      if (filter.startsWith('expiring-')) {
        const months = parseInt(filter.split('-')[1]);
        const futureDate = new Date();
        futureDate.setMonth(futureDate.getMonth() + months);

        query = query
          .eq('donation_type', 'Adoption')
          .not('end_date', 'is', null)
          .lte('end_date', futureDate.toISOString().split('T')[0]);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error loading records:', error);
        document.getElementById('records-content').innerHTML = '<div class="no-data">Error loading adoptions and donations data.</div>';
        return;
      }

      if (!data || data.length === 0) {
        document.getElementById('records-content').innerHTML = '<div class="no-data">No records found for this period and filter.</div>';
        return;
      }

      // Calculate summary statistics
      calculateSummaryStats(data);

      // Generate records table
      generateRecordsTable(data);
    }

    // Calculate summary statistics
    function calculateSummaryStats(data) {
      const totalRecords = data.length;
      const totalAdoptions = data.filter(record => record.donation_type === 'Adoption').length;
      const totalDonations = data.filter(record => record.donation_type === 'Donation').length;
      
      const totalAmount = data.reduce((sum, record) => {
        return sum + (parseFloat(record.amount) || 0);
      }, 0);

      document.getElementById('total-records').textContent = totalRecords;
      document.getElementById('total-adoptions').textContent = totalAdoptions;
      document.getElementById('total-donations').textContent = totalDonations;
      document.getElementById('total-amount').textContent = `£${totalAmount.toFixed(2)}`;
    }

    // Generate records table
    function generateRecordsTable(data) {
      const today = new Date();
      const oneMonthFromNow = new Date();
      oneMonthFromNow.setMonth(today.getMonth() + 1);

      let tableHTML = `
        <table class="records-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Animal</th>
              <th>Donor Name</th>
              <th>Email</th>
              <th>Amount</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Created By</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
      `;

      data.forEach(record => {
        const isExpiringSoon = record.end_date &&
          new Date(record.end_date) <= oneMonthFromNow &&
          new Date(record.end_date) >= today;

        const rowClass = isExpiringSoon ? 'expiring-soon' : '';

        tableHTML += `<tr class="${rowClass}">`;
        tableHTML += `<td>${new Date(record.created_at).toLocaleDateString()}</td>`;
        tableHTML += `<td><span class="record-type-${record.donation_type.toLowerCase()}">${record.donation_type}</span></td>`;
        tableHTML += `<td class="animal-name">${record.animals?.name || 'Unknown Animal'}</td>`;
        tableHTML += `<td>${record.donor_name || 'Not specified'}</td>`;
        tableHTML += `<td>${record.donor_email || 'Not provided'}</td>`;
        tableHTML += `<td class="amount">£${parseFloat(record.amount || 0).toFixed(2)}</td>`;
        tableHTML += `<td>${record.start_date ? new Date(record.start_date).toLocaleDateString() : 'N/A'}</td>`;
        tableHTML += `<td>${record.end_date ? new Date(record.end_date).toLocaleDateString() : 'N/A'}</td>`;

        tableHTML += `<td>System</td>`;
        tableHTML += `<td>${record.notes || 'No notes'}</td>`;
        tableHTML += '</tr>';
      });

      tableHTML += '</tbody></table>';
      document.getElementById('records-content').innerHTML = tableHTML;
    }

    // Initialize the report
    initializeReport();
  </script>
</body>
</html>
