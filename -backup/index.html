<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Cornish Birds of Prey – Dashboard</title>
  <link rel="stylesheet" href="styles.css" />
  
</head>
<body>
  <!-- Top Navigation -->
  <header class="top-bar">
    <h1>Cornish Birds of Prey</h1>
    <button class="menu-btn">☰</button>
  </header>

  <!-- Main Dashboard -->
  <main class="dashboard">
    <input class="search-bar" type="text" placeholder="Search animals..." />

    <section class="animal-cards">
      <!-- Cards dynamically inserted here -->
    </section>
  </main>

  <!-- Floating Button -->
  <button class="fab" onclick="document.getElementById('new_animal-modal').style.display='flex'">＋ Add a New Animal</button>

  <script type="module">
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    async function fetchAnimals() {
      const res = await fetch(`${SUPABASE_URL}/rest/v1/animals?select=*`, {
        headers: {
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`
        }
      });

      const animals = await res.json();
      const container = document.querySelector('.animal-cards');
      container.innerHTML = ''; // Clear placeholder cards

      animals.forEach(animal => {
        const card = document.createElement('article');
        card.className = 'animal-card';
        card.dataset.id = animal.id; // ✅ Used for linking to animal.html

        card.innerHTML = `
          <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="Animal Photo" />
          <div class="info">
            <h2>${animal.name}</h2>
            <p>Species: ${animal.species}</p>
            <p>Status: ${animal.status}</p>
          </div>
        `;

        container.appendChild(card);
      });
    }

    fetchAnimals();

    // ✅ Handle click to navigate to animal.html
    document.addEventListener('click', function (e) {
      const card = e.target.closest('.animal-card');
      if (card && card.dataset.id) {
        const animalId = card.dataset.id;
        window.location.href = `animal.html?id=${animalId}`;
      }
    });
  </script>

<!-- Add Animal Modal -->
<div id="new_animal-modal" class="new_animal-input">
  <div class="modal-content">
    <span class="close-modal" onclick="closeAddModal()">&times;</span>
    <iframe src="add.html" width="100%" height="100%" frameborder="0" style="border:none; overflow:hidden;"></iframe>
  </div>
</div>
</div>

<script>
 function openAddModal() {
  document.getElementById('new_animal-modal').style.display = 'flex';
}

function closeAddModal() {
  document.getElementById('new_animal-modal').style.display = 'none';
  setTimeout(() => {
    location.reload();
  }, 300);
}
</script>
</body>
</html>