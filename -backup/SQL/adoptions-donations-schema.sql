-- Adoptions & Donations Module Database Schema
-- For Cornish Birds of Prey Center Application

-- Create the adoptions_donations table
CREATE TABLE IF NOT EXISTS adoptions_donations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  donor_name TEXT NOT NULL,
  donor_email TEXT,
  donation_type TEXT NOT NULL CHECK (donation_type IN ('adoption', 'donation')),
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  notes TEXT,
  status TEXT GENERATED ALWAYS AS (
    CASE 
      WHEN end_date < CURRENT_DATE THEN 'Expired'
      WHEN end_date <= CURRENT_DATE + INTERVAL '30 days' THEN 'Expiring Soon'
      ELSE 'Active'
    END
  ) STORED,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_adoptions_donations_animal_id ON adoptions_donations(animal_id);
CREATE INDEX IF NOT EXISTS idx_adoptions_donations_donation_type ON adoptions_donations(donation_type);
CREATE INDEX IF NOT EXISTS idx_adoptions_donations_status ON adoptions_donations(status);
CREATE INDEX IF NOT EXISTS idx_adoptions_donations_start_date ON adoptions_donations(start_date);
CREATE INDEX IF NOT EXISTS idx_adoptions_donations_end_date ON adoptions_donations(end_date);

-- Enable Row Level Security
ALTER TABLE adoptions_donations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Admins can do everything with adoptions_donations" ON adoptions_donations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role = 'Admin'
    )
  );

CREATE POLICY "Staff can create, read, update adoptions_donations" ON adoptions_donations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role IN ('Staff', 'Admin')
    )
  );

CREATE POLICY "Volunteers can read adoptions_donations" ON adoptions_donations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role IN ('Volunteer', 'Staff', 'Admin')
    )
  );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_adoptions_donations_updated_at 
  BEFORE UPDATE ON adoptions_donations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE adoptions_donations IS 'Tracks animal adoptions and donations with automatic status calculation';
COMMENT ON COLUMN adoptions_donations.status IS 'Automatically calculated: Active, Expiring Soon (within 30 days), or Expired';
COMMENT ON COLUMN adoptions_donations.donation_type IS 'Either adoption or donation';
COMMENT ON COLUMN adoptions_donations.amount IS 'Donation amount in GBP';

-- Sample data for testing (optional)
INSERT INTO adoptions_donations (animal_id, donor_name, donor_email, donation_type, amount, start_date, end_date, notes) 
SELECT 
  a.id,
  'John Smith',
  '<EMAIL>',
  'adoption',
  250.00,
  CURRENT_DATE - INTERVAL '30 days',
  CURRENT_DATE + INTERVAL '335 days',
  'Annual adoption sponsorship for rehabilitation costs'
FROM animals a 
WHERE a.name = 'Mika'
LIMIT 1;

INSERT INTO adoptions_donations (animal_id, donor_name, donor_email, donation_type, amount, start_date, end_date, notes) 
SELECT 
  a.id,
  'Sarah Johnson',
  '<EMAIL>',
  'donation',
  100.00,
  CURRENT_DATE - INTERVAL '60 days',
  CURRENT_DATE + INTERVAL '10 days',
  'Monthly donation for food and care'
FROM animals a 
WHERE a.name = 'Mika'
LIMIT 1;
