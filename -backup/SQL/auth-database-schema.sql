-- Authentication & Authorization Database Schema Updates
-- For Cornish Birds of Prey Center Application

-- First, update the staff_volunteers table to link with Supabase Auth
ALTER TABLE staff_volunteers 
ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
ADD COLUMN user_role TEXT NOT NULL DEFAULT 'Volunteer' CHECK (user_role IN ('Admin', 'Staff', 'Volunteer')),
ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN last_login_at TIMESTAMPTZ,
ADD COLUMN password_reset_required BOOLEAN NOT NULL DEFAULT false;

-- Update existing records to have a default role (you'll manually set the admin)
UPDATE staff_volunteers SET user_role = 'Staff' WHERE role = 'Staff';
UPDATE staff_volunteers SET user_role = 'Volunteer' WHERE role = 'Volunteer';

-- Add user attribution to animals table
ALTER TABLE animals 
ADD COLUMN created_by UUID REFERENCES auth.users(id),
ADD COLUMN updated_by UUID REFERENCES auth.users(id);

-- Add user attribution to medical_episodes table
ALTER TABLE medical_episodes 
ADD COLUMN created_by UUID REFERENCES auth.users(id),
ADD COLUMN updated_by UUID REFERENCES auth.users(id);

-- Add user attribution to medical_interventions table  
ALTER TABLE medical_interventions 
ADD COLUMN created_by UUID REFERENCES auth.users(id),
ADD COLUMN updated_by UUID REFERENCES auth.users(id);

-- Create user_sessions table for session management
CREATE TABLE user_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  session_token TEXT NOT NULL UNIQUE,
  device_info JSONB,
  ip_address INET,
  last_activity TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create audit_log table for tracking all user actions
CREATE TABLE audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  action TEXT NOT NULL, -- 'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'LOGIN', 'LOGOUT'
  table_name TEXT NOT NULL,
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user_preferences table for storing user settings
CREATE TABLE user_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  theme TEXT DEFAULT 'default',
  language TEXT DEFAULT 'en',
  timezone TEXT DEFAULT 'Europe/London',
  notifications_enabled BOOLEAN DEFAULT true,
  two_factor_enabled BOOLEAN DEFAULT false,
  biometric_enabled BOOLEAN DEFAULT false,
  auto_logout_minutes INTEGER DEFAULT 10,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_staff_volunteers_user_id ON staff_volunteers(user_id);
CREATE INDEX idx_staff_volunteers_user_role ON staff_volunteers(user_role);
CREATE INDEX idx_staff_volunteers_is_active ON staff_volunteers(is_active);

CREATE INDEX idx_animals_created_by ON animals(created_by);
CREATE INDEX idx_animals_updated_by ON animals(updated_by);

CREATE INDEX idx_medical_episodes_created_by ON medical_episodes(created_by);
CREATE INDEX idx_medical_interventions_created_by ON medical_interventions(created_by);

CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_last_activity ON user_sessions(last_activity);

CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_log_table_name ON audit_log(table_name);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);

-- Enable RLS on new tables
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_sessions
CREATE POLICY "Users can view their own sessions" ON user_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sessions" ON user_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sessions" ON user_sessions
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for audit_log (read-only for users, admins can see all)
CREATE POLICY "Users can view their own audit logs" ON audit_log
  FOR SELECT USING (
    auth.uid() = user_id OR 
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role = 'Admin'
    )
  );

-- RLS Policies for user_preferences
CREATE POLICY "Users can manage their own preferences" ON user_preferences
  FOR ALL USING (auth.uid() = user_id);

-- Update RLS policies for existing tables to include role-based access

-- Animals table policies
DROP POLICY IF EXISTS "Allow all operations on animals" ON animals;

CREATE POLICY "Admins can do everything with animals" ON animals
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role = 'Admin'
    )
  );

CREATE POLICY "Staff can create, read, update animals" ON animals
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role IN ('Staff', 'Admin')
    )
  );

CREATE POLICY "Volunteers can read animals and add logs" ON animals
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role IN ('Volunteer', 'Staff', 'Admin')
    )
  );

-- Medical episodes policies  
DROP POLICY IF EXISTS "Allow all operations on medical_episodes" ON medical_episodes;

CREATE POLICY "Admins and Staff can manage medical episodes" ON medical_episodes
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role IN ('Admin', 'Staff')
    )
  );

CREATE POLICY "Volunteers can view open medical episodes" ON medical_episodes
  FOR SELECT USING (
    status != 'resolved' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role IN ('Volunteer', 'Staff', 'Admin')
    )
  );

-- Staff volunteers policies
DROP POLICY IF EXISTS "Allow all operations on staff_volunteers" ON staff_volunteers;

CREATE POLICY "Admins can manage all staff and volunteers" ON staff_volunteers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role = 'Admin'
    )
  );

CREATE POLICY "Users can view their own profile" ON staff_volunteers
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Staff can view other staff and volunteers" ON staff_volunteers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() AND user_role IN ('Staff', 'Admin')
    )
  );

-- Create function to automatically create user preferences when a user is linked
CREATE OR REPLACE FUNCTION create_user_preferences()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.user_id IS NOT NULL AND OLD.user_id IS NULL THEN
    INSERT INTO user_preferences (user_id) VALUES (NEW.user_id);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic user preferences creation
CREATE TRIGGER create_user_preferences_trigger
  AFTER UPDATE ON staff_volunteers
  FOR EACH ROW
  EXECUTE FUNCTION create_user_preferences();

-- Create function to log user actions
CREATE OR REPLACE FUNCTION log_user_action()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO audit_log (user_id, action, table_name, record_id, new_values)
    VALUES (auth.uid(), 'CREATE', TG_TABLE_NAME, NEW.id, to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_log (user_id, action, table_name, record_id, old_values, new_values)
    VALUES (auth.uid(), 'UPDATE', TG_TABLE_NAME, NEW.id, to_jsonb(OLD), to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_log (user_id, action, table_name, record_id, old_values)
    VALUES (auth.uid(), 'DELETE', TG_TABLE_NAME, OLD.id, to_jsonb(OLD));
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create audit triggers for all main tables
CREATE TRIGGER audit_animals_trigger
  AFTER INSERT OR UPDATE OR DELETE ON animals
  FOR EACH ROW EXECUTE FUNCTION log_user_action();

CREATE TRIGGER audit_medical_episodes_trigger
  AFTER INSERT OR UPDATE OR DELETE ON medical_episodes
  FOR EACH ROW EXECUTE FUNCTION log_user_action();

CREATE TRIGGER audit_medical_interventions_trigger
  AFTER INSERT OR UPDATE OR DELETE ON medical_interventions
  FOR EACH ROW EXECUTE FUNCTION log_user_action();

CREATE TRIGGER audit_staff_volunteers_trigger
  AFTER INSERT OR UPDATE OR DELETE ON staff_volunteers
  FOR EACH ROW EXECUTE FUNCTION log_user_action();

-- Comments for documentation
COMMENT ON TABLE user_sessions IS 'Tracks active user sessions for security and auto-logout';
COMMENT ON TABLE audit_log IS 'Comprehensive audit trail of all user actions';
COMMENT ON TABLE user_preferences IS 'User-specific settings and preferences';
COMMENT ON COLUMN staff_volunteers.user_id IS 'Links to Supabase auth.users table';
COMMENT ON COLUMN staff_volunteers.user_role IS 'Admin, Staff, or Volunteer access level';
COMMENT ON COLUMN staff_volunteers.is_active IS 'Whether user account is active';
COMMENT ON COLUMN staff_volunteers.password_reset_required IS 'Forces password reset on next login';

-- Note: After running this schema, you'll need to:
-- 1. Manually create your admin user in Supabase Auth
-- 2. Link your admin user to a staff_volunteers record with user_role = 'Admin'
-- 3. Configure Supabase Auth settings for password policies and 2FA
