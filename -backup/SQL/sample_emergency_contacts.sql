-- Sample Emergency Contacts Data
-- Run this in your Supabase SQL Editor to add test data

INSERT INTO emergency_contacts (contact_name, role, phone, alt_phone, email, priority_level, notes, is_active) VALUES
  ('Dr. <PERSON>', 'Lead Vet', '01234 567890', '07700 900001', '<EMAIL>', 'high', 'Out-of-hours vet service available – call mobile', true),

  ('Emergency Services', 'Fire Service', '999', NULL, '<EMAIL>', 'high', 'For fire emergencies and animal rescues from heights.', true),

  ('Cornwall Wildlife Rescue', 'Wildlife Rescue Service', '01872 123456', '01872 654321', '<EMAIL>', 'high', 'Specialized in bird rescue and rehabilitation. Call for injured wildlife.', true),

  ('RSPCA Cornwall', 'Animal Welfare', '0300 123 4999', NULL, '<EMAIL>', 'medium', 'General animal welfare support and advice.', true),

  ('Local Police', 'Emergency Services', '101', '999', NULL, 'medium', 'Non-emergency: 101, Emergency: 999. For security issues.', true),

  ('<PERSON>', 'Sanctuary Manager', '************', '************', '<EMAIL>', 'medium', 'Primary contact for all sanctuary matters. Available most hours.', true),

  ('Mike Wilson', 'Maintenance Manager', '************', NULL, '<EMAIL>', 'low', 'For facility maintenance issues during business hours.', true),

  ('Electric Company', 'Utilities', '0800 111 999', NULL, NULL, 'low', 'For power outages affecting the sanctuary.', true),

  ('Water Company', 'Utilities', '0800 783 4444', NULL, '<EMAIL>', 'low', 'For water supply issues.', true);

-- Display success message
SELECT 'Sample emergency contacts added successfully!' as message,
       COUNT(*) as total_contacts 
FROM emergency_contacts 
WHERE is_active = true;
