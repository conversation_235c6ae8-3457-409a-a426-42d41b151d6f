-- Create staff profile for the new user
-- Run this AFTER creating the user in Supabase Auth UI

-- First, check what users exist (for verification)
SELECT id, email, created_at FROM auth.users ORDER BY created_at DESC LIMIT 5;

-- Create a staff_volunteers record for your user
INSERT INTO staff_volunteers (
    user_id,
    name,
    role,
    user_role,
    position,
    email,
    phone_number,
    start_date,
    status,
    is_active,
    password_reset_required
)
SELECT
    id as user_id,
    '<PERSON>' as name,
    'Staff' as role,
    'Admin' as user_role,
    'System Administrator' as position,
    email,
    NULL as phone_number,
    CURRENT_DATE as start_date,
    'Active' as status,
    true as is_active,
    false as password_reset_required
FROM auth.users
WHERE email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM staff_volunteers WHERE user_id = auth.users.id
);

-- Alternative: If you want to make ALL existing auth users into admins (be careful!)
-- Uncomment the following if you want to do this instead:

/*
INSERT INTO staff_volunteers (
    user_id,
    name,
    role,
    user_role,
    position,
    email,
    phone_number,
    start_date,
    status,
    is_active,
    password_reset_required
)
SELECT 
    id as user_id,
    'Admin User' as name,
    'Staff' as role,
    'Admin' as user_role,
    'System Administrator' as position,
    email,
    NULL as phone_number,
    CURRENT_DATE as start_date,
    'Active' as status,
    true as is_active,
    false as password_reset_required
FROM auth.users 
WHERE NOT EXISTS (
    SELECT 1 FROM staff_volunteers WHERE user_id = auth.users.id
);
*/

-- Verify the insertion worked
SELECT 
    sv.name,
    sv.email,
    sv.user_role,
    sv.is_active,
    au.email as auth_email
FROM staff_volunteers sv
JOIN auth.users au ON sv.user_id = au.id
WHERE sv.user_role = 'Admin';
