-- Medical Welfare Tracker Database Schema
-- Run these SQL commands in your Supabase SQL editor to create the required tables

-- Create medical_episodes table
CREATE TABLE IF NOT EXISTS medical_episodes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('injury', 'illness', 'surgery', 'rehabilitation', 'preventive', 'other')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    date_discovered DATE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'monitoring', 'resolved')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create medical_interventions table
CREATE TABLE IF NOT EXISTS medical_interventions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    episode_id UUID NOT NULL REFERENCES medical_episodes(id) ON DELETE CASCADE,
    intervention_date TIMESTAMP WITH TIME ZONE NOT NULL,
    intervention_type VARCHAR(50) NOT NULL CHECK (intervention_type IN ('examination', 'medication', 'treatment', 'surgery', 'therapy', 'monitoring', 'other')),
    description TEXT NOT NULL,
    staff_member VARCHAR(255),
    outcome TEXT,
    next_steps TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_medical_episodes_animal_id ON medical_episodes(animal_id);
CREATE INDEX IF NOT EXISTS idx_medical_episodes_date_discovered ON medical_episodes(date_discovered);
CREATE INDEX IF NOT EXISTS idx_medical_episodes_status ON medical_episodes(status);
CREATE INDEX IF NOT EXISTS idx_medical_interventions_episode_id ON medical_interventions(episode_id);
CREATE INDEX IF NOT EXISTS idx_medical_interventions_date ON medical_interventions(intervention_date);

-- Create updated_at trigger function (if it doesn't exist)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_medical_episodes_updated_at 
    BEFORE UPDATE ON medical_episodes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medical_interventions_updated_at 
    BEFORE UPDATE ON medical_interventions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) if needed
ALTER TABLE medical_episodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_interventions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (adjust based on your authentication needs)
-- For now, allowing all operations - you may want to restrict this based on user roles

CREATE POLICY "Allow all operations on medical_episodes" ON medical_episodes
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on medical_interventions" ON medical_interventions
    FOR ALL USING (true) WITH CHECK (true);

-- Grant permissions to anon and authenticated users (adjust as needed)
GRANT ALL ON medical_episodes TO anon, authenticated;
GRANT ALL ON medical_interventions TO anon, authenticated;

-- Sample data for testing (optional - remove if not needed)
-- INSERT INTO medical_episodes (animal_id, title, type, severity, description, date_discovered, status)
-- VALUES 
--     ((SELECT id FROM animals LIMIT 1), 'Broken Wing', 'injury', 'high', 'Left wing appears to be fractured, bird unable to fly', '2024-01-15', 'active'),
--     ((SELECT id FROM animals LIMIT 1), 'Eye Infection', 'illness', 'medium', 'Discharge from right eye, appears inflamed', '2024-01-20', 'monitoring');
