<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Add New Animal</title>
  <link rel="stylesheet" href="styles.css" />
  <style>
    body {
      margin: 0;
      padding: 0;
      background: transparent;
    }

    .form-wrapper {
      margin: 0;
      padding: 0;
      background: transparent;
      box-shadow: none;
    }

    .form-wrapper h1 {
      margin-top: 0;
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }

    .form-wrapper form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .form-wrapper label {
      display: flex;
      flex-direction: column;
      font-weight: bold;
    }

    .form-wrapper input,
    .form-wrapper select,
    .form-wrapper textarea {
      padding: 0.6rem;
      border-radius: 8px;
      border: 1px solid #ccc;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
      font-size: 1rem;
    }

    .form-wrapper button[type="submit"] {
      padding: 0.6rem 1.2rem;
      background-color: #446c35;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: bold;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      width: fit-content;
    }

    .form-wrapper button[type="submit"]:hover {
      background-color: #365b2b;
    }
  </style>
</head>
<body>
  <main class="form-wrapper">
    <h1>Add New Animal</h1>
    <form id="animal-form">
      <label>
        Name:
        <input type="text" name="name" required />
      </label>

      <label>
        Species:
        <input type="text" name="species" required />
      </label>

      <label>
        Status:
        <select name="status" required>
          <option value="">--Select--</option>
          <option value="All Clear">All Clear</option>
          <option value="Under Observation">Under Observation</option>
          <option value="Unwell">Unwell</option>
          <option value="Vet Booked">Vet Booked</option>
          <option value="In Treatment">In Treatment</option>
          <option value="Recovery">Recovery</option>
          <option value="Ongoing Condition">Ongoing Condition</option>
          <option value="Palliative">Palliative</option>
          <option value="Quarantined">Quarantined</option>
          <option value="Transferred">Transferred</option>
          <option value="Deceased">Deceased</option>
        </select>
      </label>

      <label>
        Photo URL:
        <input type="url" name="photo_url" placeholder="https://..." />
      </label>

      <label>
        Notes:
        <textarea name="notes" rows="4"></textarea>
      </label>

      <button type="submit">Save Animal</button>
    </form>
  </main>

  <script type="module">
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    const form = document.getElementById('animal-form');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const data = Object.fromEntries(new FormData(form).entries());

      const res = await fetch(`${SUPABASE_URL}/rest/v1/animals`, {
        method: 'POST',
        headers: {
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`,
          'Content-Type': 'application/json',
          Prefer: 'return=representation'
        },
        body: JSON.stringify(data)
      });

      const json = await res.json();
      if (res.ok && json[0]) {
        const newId = json[0].id;
        window.location.href = `animal.html?id=${newId}`;
      } else {
        alert("Something went wrong");
        console.error("Supabase error:", res.status, json);
      }
    });
  </script>
</body>
</html>