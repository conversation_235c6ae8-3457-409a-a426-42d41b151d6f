/* General Base Styles */
body {
  font-family: sans-serif;
  margin: 0;
  padding: 0;
  background: #f4f4f4;
  color: #222;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Header */
.top-bar {
  background: #446c35;
  color: white;
  padding: 1rem;
  text-align: center;
  font-size: 1.25rem;
}

.menu-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  font-size: 1.5rem;
  color: white;
  cursor: pointer;
}

/* Dashboard */
.dashboard {
  padding: 1rem;
}

.search-bar {
  width: 100%;
  max-width: 400px;
  margin: 1rem auto;
  display: block;
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1rem;
}

/* Animal Cards */
.animal-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.animal-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.animal-card:hover {
  transform: scale(1.02);
}

.animal-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.animal-card .info {
  padding: 1rem;
}

/* Add Animal Modal */
#new_animal-modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 999;
  justify-content: center;
  align-items: center;
}

.new_animal-input .modal-content {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  width: 80%;
  max-width: 600px;
  height: 750px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.new_animal-input .close-modal {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
}

/* Add Form Page */
.form-wrapper {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.form-wrapper form label {
  display: block;
  margin: 1rem 0 0.5rem;
  font-weight: bold;
}

form label {
  display: block;
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

input[type="text"],
input[type="url"],
textarea,
select {
  width: 100%;
  margin-top: 0.5rem;
  padding: 0.6rem;
  border-radius: 8px;
  border: 1px solid #ccc;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  font-size: 1rem;
  box-sizing: border-box;
}

/* Modern Button Styles */
button[type="submit"],
.fab,
.floating-button,
.print-button,
#toggle-logs {
  margin-top: 1rem;
  padding: 0.6rem 1.2rem;
  background-color: #446c35;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

button[type="submit"]:hover,
.print-button:hover,
.fab:hover,
.floating-button:hover,
#toggle-logs:hover {
  background-color: #365b2b;
}

/* Floating Button */
.fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #446c35;
  color: white;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.fab:hover {
  background-color: #365b2b;
}

/* Floating Action Button Section */
.floating-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 998;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Animal Detail Page */

.animal-detail {
  max-width: 600px;
  margin: 2rem auto;
  padding: 1.5rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  text-align: center;
}

.animal-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: left;
}

.animal-header h1 {
  margin-bottom: 1rem;
}

.animal-detail img {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.animal-info {
  text-align: left;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-row {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
  line-height: 1.5;
}

.info-row strong {
  min-width: 80px;
  font-weight: bold;
}

.note-row span {
  white-space: pre-line;
}

.qr-container {
  margin: 2rem auto 1rem;
}

/* Custom Checkbox Styles */
.custom-checkbox {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}

.custom-checkbox input[type="checkbox"] {
  display: none;
}

.custom-checkbox .checkmark {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background-color: #f44336; /* Red */
  border: 1px solid #ccc;
  display: inline-block;
  position: relative;
  transition: all 0.2s ease;
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark {
  background-color: #4CAF50; /* Green */
  border: 1px solid #4CAF50;
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✅';
  text-align: center;
  color: white;
  font-size: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Log Entries */
.log-entry {
  text-align: left;
  background: #f9f9f9;
  padding: 0.75rem;
  border: 1px solid #ddd;
  margin-top: 0.5rem;
  border-radius: 6px;
}

/* Modal Styles */
.modal,
#log-modal,
#edit-animal-modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 999;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  width: 80%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-content h2 {
  margin-top: 0;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
}

/* Print Overrides */
@media print {
  .print-button,
  .floating-button,
  .floating-actions,
  #log-feedback,
  #recent-logs-section,
  #log-modal,
  #edit-animal-modal {
    display: none !important;
  }
}

/* Log Display */
.log-table {
  width: 95%;
  border-collapse: collapse;
  border: 2px solid #2e6b2e;
  border-radius: 10px;
  overflow: hidden;
}

.log-table th,
.log-table td {
  padding: 0.75rem;
  text-align: left;
  vertical-align: top;
  border: 1px solid #2e6b2e;
}

.log-table th {
  width: 120px;
  font-weight: bold;
  background: #f8f8f8;
}

/* Header Styling */
.top-bar {
  background: #446c35;
  color: white;
  padding: 1rem;
  font-size: 1.25rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.top-bar .title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
}

.menu-btn {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  color: white;
  cursor: pointer;
  margin-left: 0.5rem;
}

.back-link {
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  margin-right: 0.5rem;
}

.back-link:hover {
  text-decoration: underline;
}

.side-menu {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  background: #344d2f;
  color: white;
  width: 220px;
  height: 100%;
  padding: 2rem 1rem;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.side-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.side-menu li {
  margin-bottom: 1rem;
}

.side-menu a {
  color: white;
  text-decoration: none;
  font-weight: 500;
}

.side-menu a:hover {
  text-decoration: underline;
}

/* Floating Action Menu Styles */
.floating-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.fab-main {
  background-color: #446c35;
  color: white;
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  font-size: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: background 0.3s ease;
}

.fab-main:hover {
  background-color: #365b2b;
}

.fab-sub {
  background-color: #446c35;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.4rem 0.6rem;
  font-size: 0.9rem;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(0,0,0,0.15);
  opacity: 0;
  transform: scale(0.95);
  transform-origin: bottom right;
  transition: all 0.2s ease;
  pointer-events: none;
}

.fab-sub.show {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

/* Material Icons */
.material-icons {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Positioning helper */
.fab-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}