<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Debug Dashboard – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <!-- Top Navigation -->
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Debug Dashboard</h1>
      <div id="user-display" class="user-display">
        <div id="auth-status">Checking auth...</div>
      </div>
    </header>

    <!-- Debug Information -->
    <div style="background: rgba(255,255,255,0.8); padding: 1rem; margin: 1rem 0; border-radius: 10px;">
      <h3>Debug Information:</h3>
      <div id="debug-info">Loading...</div>
    </div>

    <!-- Main Content -->
    <main class="dashboard-main">
      <input class="search-bar" type="text" placeholder="Search animals..." />
      
      <section class="animal-cards" id="animal-cards">
        <div style="text-align: center; padding: 2rem;">
          <div id="loading-status">Loading animals...</div>
        </div>
      </section>
    </main>
  </div>

  <script type="module">
    console.log('🔍 Debug Dashboard Starting...');
    
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    let debugInfo = [];
    let supabase = null;

    function addDebugInfo(message) {
      console.log('🔍', message);
      debugInfo.push(`${new Date().toLocaleTimeString()}: ${message}`);
      document.getElementById('debug-info').innerHTML = debugInfo.join('<br>');
    }

    async function initializeDebug() {
      try {
        addDebugInfo('Starting initialization...');
        
        // Import Supabase client
        addDebugInfo('Importing Supabase client...');
        const { createClient } = await import('https://cdn.skypack.dev/@supabase/supabase-js@2');
        supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
        addDebugInfo('✅ Supabase client created');

        // Check authentication
        addDebugInfo('Checking authentication...');
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          addDebugInfo(`❌ Session error: ${sessionError.message}`);
          return;
        }

        if (session) {
          addDebugInfo(`✅ User authenticated: ${session.user.email}`);
          document.getElementById('auth-status').textContent = `Logged in: ${session.user.email}`;
          
          // Try to load user profile
          addDebugInfo('Loading user profile...');
          const { data: profile, error: profileError } = await supabase
            .from('staff_volunteers')
            .select('*')
            .eq('user_id', session.user.id)
            .single();

          if (profileError) {
            addDebugInfo(`⚠️ Profile error: ${profileError.message}`);
          } else {
            addDebugInfo(`✅ Profile loaded: ${profile.name} (${profile.user_role})`);
          }
        } else {
          addDebugInfo('❌ No active session');
          document.getElementById('auth-status').textContent = 'Not logged in';
          return;
        }

        // Try to fetch animals
        addDebugInfo('Fetching animals...');
        document.getElementById('loading-status').textContent = 'Fetching animals...';
        
        const { data: animals, error: animalsError } = await supabase
          .from('animals')
          .select('*');

        if (animalsError) {
          addDebugInfo(`❌ Animals error: ${animalsError.message}`);
          document.getElementById('loading-status').textContent = `Error: ${animalsError.message}`;
          return;
        }

        addDebugInfo(`✅ Animals loaded: ${animals.length} found`);
        
        if (animals.length === 0) {
          document.getElementById('loading-status').textContent = 'No animals found in database';
          return;
        }

        // Display animals
        const container = document.getElementById('animal-cards');
        container.innerHTML = '';

        animals.forEach((animal, index) => {
          addDebugInfo(`Rendering animal ${index + 1}: ${animal.name}`);
          
          const card = document.createElement('article');
          card.className = 'animal-card';
          card.innerHTML = `
            <img src="${animal.photo_url || 'https://via.placeholder.com/300x200?text=No+Photo'}" alt="${animal.name}" />
            <div class="info">
              <h2>${animal.name}</h2>
              <p>Species: ${animal.species}</p>
              <p>Status: ${animal.status}</p>
            </div>
          `;
          container.appendChild(card);
        });

        addDebugInfo(`✅ All ${animals.length} animals rendered successfully`);

      } catch (error) {
        addDebugInfo(`❌ Fatal error: ${error.message}`);
        console.error('Debug error:', error);
      }
    }

    // Start the debug process
    initializeDebug();

    // Menu toggle function
    function toggleMenu() {
      console.log('Menu toggle clicked');
    }

    // Make function globally available
    window.toggleMenu = toggleMenu;

  </script>
</body>
</html>
