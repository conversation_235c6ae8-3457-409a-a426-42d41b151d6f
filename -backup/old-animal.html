<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Animal Record</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="top-bar">
  <button class="menu-btn" onclick="toggleMenu()">☰</button>
  <span class="title">Animal Record</span>
  <a href="index.html" class="back-link">← Back to Dashboard</a>
</header>

<nav id="side-menu" class="side-menu">
  <ul>
    <li><a href="index.html">Dashboard</a></li>
    <li><a href="risk-assessments.html">Risk Assessments</a></li>
    <li><a href="animal-list.html">All Animals</a></li>
    <!-- Add other nav links here -->
  </ul>
</nav>  
  <main class="animal-detail">
  <div class="animal-header">
  <h1 id="animal-name">Loading...</h1>
  <img id="animal-photo" src="assets/images/placeholder.jpg" alt="Animal Photo" />
  <div class="animal-info">
    <div class="info-row">
      <strong>Species:</strong>
      <span id="animal-species"></span>
    </div>
    <div class="info-row">
      <strong>Status:</strong>
      <span id="animal-status"></span>
    </div>
    <div class="info-row note-row">
      <strong>Notes:</strong>
      <span id="animal-notes"></span>
    </div>
  </div>
</div>

  <div class="qr-container">
    <canvas id="qrcode"></canvas>
  </div>

  <hr>

  <section>
    <h3>Today's Logs</h3>
    <div id="today-logs"></div>
  </section>

  <section>
    <button id="toggle-logs">Show Recent Logs</button>
    <div id="recent-logs-section" style="display:none;">
      <h3>Recent Logs</h3>
      <div id="recent_logs"></div>
    </div>
  </section>
</main>

  <!-- Floating Action Buttons -->
  <div class="floating-actions">
    <button class="floating-button" onclick="document.getElementById('log-modal').style.display='flex'">＋ Add Log</button>
    <button class="floating-button" onclick="openEditModal()">✏️ Edit Animal</button>
    <button class="floating-button" onclick="window.print()">🖨 Print</button>
  </div>

  <!-- Log Modal -->
  <div id="log-modal">
    <div class="modal-content">
      <span class="close-modal" onclick="document.getElementById('log-modal').style.display='none'">✖</span>
      <h2>Add Daily Log</h2>
      <form id="log-form">
        <label class="custom-checkbox">
          <input type="checkbox" name="fed" />
          <span class="checkmark"></span> Fed
        </label>
        <label class="custom-checkbox">
          <input type="checkbox" name="cleaned" />
          <span class="checkmark"></span> Cleaned
        </label><br>
        <label>Medication Given:<br>
          <input type="text" name="medication" placeholder="e.g. Wormer, Painkiller" />
        </label><br>
        <label>Notes:<br>
          <textarea name="notes" rows="3" placeholder="e.g. Slight limp, feathers checked"></textarea>
        </label>
        <button type="submit">Add Log</button>
      </form>
      <div id="log-feedback" style="margin-top:1rem;"></div>
    </div>
  </div>

  <!-- Edit Modal -->
  <div id="edit-animal-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeEditModal()">✖</span>
      <h2>Edit Animal</h2>
      <form id="edit-animal-form">
        <label>Name:<br>
          <input type="text" name="name" required />
        </label><br>
        <label>Species:<br>
          <input type="text" name="species" required />
        </label><br>
        <label>Status:<br>
          <select name="status" required>
            <option value="All Clear">All Clear</option>
            <option value="Under Observation">Under Observation</option>
            <option value="Unwell">Unwell</option>
            <option value="Vet Booked">Vet Booked</option>
            <option value="In Treatment">In Treatment</option>
            <option value="Recovery">Recovery</option>
            <option value="Ongoing Condition">Ongoing Condition</option>
            <option value="Palliative">Palliative</option>
            <option value="Quarantined">Quarantined</option>
            <option value="Transferred">Transferred</option>
            <option value="Deceased">Deceased</option>
          </select>
        </label><br>
        <label>Photo URL:<br>
          <input type="url" name="photo_url" />
        </label><br>
        <label>Notes:<br>
          <textarea name="notes" rows="4"></textarea>
        </label><br>
        <button type="submit">Save Changes</button>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>

  <script type="module">
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    const urlParams = new URLSearchParams(window.location.search);
    const animalId = urlParams.get('id');
    let currentAnimal = null;

    async function loadAnimal() {
      const res = await fetch(`${SUPABASE_URL}/rest/v1/animals?id=eq.${animalId}&select=*`, {
        headers: {
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`
        }
      });

      const [animal] = await res.json();
      if (!animal) {
        document.body.innerHTML = '<h2>Animal not found.</h2>';
        return;
      }

      currentAnimal = animal;
      document.getElementById('animal-name').textContent = animal.name;
      document.getElementById('animal-species').textContent = animal.species;
      document.getElementById('animal-status').textContent = animal.status;
      document.getElementById('animal-notes').textContent = animal.notes || '—';
      document.getElementById('animal-photo').src = animal.photo_url || 'assets/images/placeholder.jpg';

      const qrLink = `${window.location.origin}${window.location.pathname}?id=${animal.id}`;
      QRCode.toCanvas(document.getElementById('qrcode'), qrLink, { width: 100 });
    }

    function formatLog(log) {
  const dateStr = new Date(log.created_on || '').toLocaleDateString('en-UK', {
    day: '2-digit', month: '2-digit', year: 'numeric'
  });

  return `
    <div class="log-entry">
      <table class="log-table">
        <tr><th>Date:</th><td>${dateStr}</td></tr>
        <tr><th>Fed:</th><td>${log.fed ? 'Yes' : 'No'}</td></tr>
        <tr><th>Cleaned:</th><td>${log.cleaned ? 'Yes' : 'No'}</td></tr>
        <tr><th>Medication:</th><td>${log.medication || 'N/A'}</td></tr>
        <tr><th>Notes:</th><td>${log.notes || '—'}</td></tr>
      </table>
    </div>
  `;
}

    async function loadLogs() {
      const res = await fetch(`${SUPABASE_URL}/rest/v1/daily_logs?animal_id=eq.${animalId}&order=created_on.desc`, {
        headers: {
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`
        }
      });

      const logs = await res.json();
      const today = new Date().toISOString().split('T')[0];
      const todayLogs = logs.filter(log => (log.created_on || '').split('T')[0] === today);
      const pastLogs = logs.filter(log => (log.created_on || '').split('T')[0] !== today);

      document.getElementById('today-logs').innerHTML = todayLogs.length
        ? todayLogs.map(formatLog).join('')
        : '<p>No logs today yet.</p>';

      document.getElementById('recent_logs').innerHTML = pastLogs.map(formatLog).join('');
    }

    function openEditModal() {
      const form = document.getElementById('edit-animal-form');
      form.name.value = currentAnimal.name;
      form.species.value = currentAnimal.species;
      form.status.value = currentAnimal.status;
      form.photo_url.value = currentAnimal.photo_url || '';
      form.notes.value = currentAnimal.notes || '';
      document.getElementById('edit-animal-modal').style.display = 'flex';
    }

    function closeEditModal() {
      document.getElementById('edit-animal-modal').style.display = 'none';
    }

    document.getElementById('edit-animal-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);

      const updatedAnimal = {
        name: formData.get("name"),
        species: formData.get("species"),
        status: formData.get("status"),
        photo_url: formData.get("photo_url"),
        notes: formData.get("notes")
      };

      const res = await fetch(`${SUPABASE_URL}/rest/v1/animals?id=eq.${animalId}`, {
        method: 'PATCH',
        headers: {
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`,
          'Content-Type': 'application/json',
          Prefer: 'return=representation'
        },
        body: JSON.stringify(updatedAnimal)
      });

      if (res.ok) {
        closeEditModal();
        location.reload();
      } else {
        alert("Update failed");
        console.error(await res.json());
      }
    });

    document.getElementById('log-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);

      const logData = {
        animal_id: animalId,
        fed: formData.has('fed'),
        cleaned: formData.has('cleaned'),
        medication: formData.get('medication'),
        notes: formData.get('notes')
      };

      const res = await fetch(`${SUPABASE_URL}/rest/v1/daily_logs`, {
        method: 'POST',
        headers: {
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`,
          'Content-Type': 'application/json',
          Prefer: 'return=representation'
        },
        body: JSON.stringify(logData)
      });

      if (res.ok) {
        document.getElementById('log-feedback').textContent = '✅ Log saved.';
        e.target.reset();
        document.getElementById('log-modal').style.display = 'none';
        loadLogs();
      } else {
        document.getElementById('log-feedback').textContent = '❌ Error saving log.';
        console.error(await res.json());
      }
    });

    document.getElementById('toggle-logs').addEventListener('click', () => {
      const section = document.getElementById('recent-logs-section');
      section.style.display = section.style.display === 'none' ? 'block' : 'none';
      document.getElementById('toggle-logs').textContent = section.style.display === 'none' ? 'Show Recent Logs' : 'Hide Recent Logs';
    });

    loadAnimal();
    loadLogs();
    window.openEditModal = openEditModal;
    window.closeEditModal = closeEditModal;
  </script>
</body>
</html>