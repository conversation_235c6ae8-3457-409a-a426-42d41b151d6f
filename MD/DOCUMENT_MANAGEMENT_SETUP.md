# Document Management Setup Guide

## Overview
This guide explains how to set up the Document Management feature for the Cornish Birds of Prey application.

## Features Implemented
- ✅ Document upload with drag-and-drop functionality
- ✅ File type validation (.doc, .docx, .pdf only)
- ✅ Document categorization (Policy Documents, Risk Assessments, Emergency Procedures)
- ✅ Grid and list view options
- ✅ Category filtering
- ✅ Download functionality
- ✅ Delete functionality with confirmation
- ✅ Responsive design
- ✅ Frosted glass styling consistent with app theme
- ✅ Navigation menu integration

## Supabase Setup Required

### 1. Create Documents Table
Run the SQL script `supabase-documents-setup.sql` in your Supabase SQL editor:

```sql
-- The script creates:
-- - documents table with proper schema
-- - indexes for performance
-- - RLS policies
-- - triggers for updated_at timestamp
```

### 2. Create Storage Bucket
1. Go to your Supabase Dashboard
2. Navigate to Storage
3. Create a new bucket named `documents`
4. Configure bucket settings:
   - **Public**: Set to public for direct access, or private for more security
   - **File size limit**: Recommended 50MB
   - **Allowed MIME types**: 
     - `application/pdf`
     - `application/msword`
     - `application/vnd.openxmlformats-officedocument.wordprocessingml.document`

### 3. Storage Policies
Create the following policies in the Storage section:

**Policy 1: Allow authenticated users to upload documents**
- Operation: INSERT
- Target roles: authenticated
- Policy: `true` (or customize based on your needs)

**Policy 2: Allow authenticated users to view documents**
- Operation: SELECT  
- Target roles: authenticated
- Policy: `true`

**Policy 3: Allow authenticated users to delete documents**
- Operation: DELETE
- Target roles: authenticated  
- Policy: `true`

## File Structure
```
/
├── documents.html              # Main document management page
├── supabase-documents-setup.sql # Database setup script
├── styles.css                  # Updated with document management styles
├── index.html                  # Updated with navigation menu
├── animal.html                 # Updated with navigation menu
├── medical-tracker.html        # Updated with navigation menu
└── DOCUMENT_MANAGEMENT_SETUP.md # This file
```

## Navigation Menu
The hamburger menu has been implemented across all pages with the following structure:
- Dashboard (index.html)
- Document Management (documents.html)
- Reports (placeholder)
- Settings (placeholder)

## Usage Instructions

### Uploading Documents
1. Navigate to Document Management from the hamburger menu
2. Select a document category from the dropdown
3. Either:
   - Click "Choose Files" to browse for files
   - Drag and drop files directly onto the upload area
4. Only .doc, .docx, and .pdf files are accepted
5. Upload progress will be shown during the process

### Viewing Documents
- Switch between Grid and List views using the toggle buttons
- Filter documents by category using the filter buttons
- Documents show filename, category, file size, and upload date

### Managing Documents
- **Download**: Click the download icon to download any document
- **Delete**: Click the delete icon and confirm to remove a document
- Deleted documents are removed from both the database and storage

## Styling
The document management page uses the same frosted glass theme as the rest of the application:
- Semi-transparent backgrounds with blur effects
- Blue-green-yellow gradient accents
- Consistent button and form styling
- Responsive design for mobile devices

## Security Considerations
- File type validation on both frontend and backend
- File size limits enforced by Supabase storage
- RLS policies control access to documents
- All uploads require authentication

## Troubleshooting

### Common Issues
1. **Upload fails**: Check Supabase storage bucket permissions
2. **Documents don't load**: Verify database table exists and RLS policies are correct
3. **Download doesn't work**: Check storage bucket public access settings
4. **Menu doesn't open**: Ensure JavaScript is enabled and no console errors

### Error Messages
- "Please select a document category": Choose a category before uploading
- "Invalid file types detected": Only .doc, .docx, and .pdf files are allowed
- "Error uploading document": Check Supabase connection and storage permissions
- "Error loading documents": Check database connection and table structure

## Future Enhancements
Potential improvements that could be added:
- Document versioning
- Full-text search within documents
- Document preview functionality
- Bulk upload/delete operations
- Document sharing and permissions
- Audit trail for document changes
